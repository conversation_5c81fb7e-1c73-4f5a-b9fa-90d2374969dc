<?php
/**
 * PFG Cloudflare Integration Module
 * 
 * @category   PFG
 * @package    PFG_Cloudflare
 * <AUTHOR> Development Team
 */

class PFG_Cloudflare_Block_Adminhtml_System_Config_AjaxHandler extends Mage_Adminhtml_Block_System_Config_Form_Field
{
    /**
     * Set template to itself
     */
    protected function _prepareLayout()
    {
        parent::_prepareLayout();
        if (!$this->getTemplate()) {
            $this->setTemplate('pfg_cloudflare/system/config/ajax_handler.phtml');
        }
        return $this;
    }

    /**
     * Unset some non-related element parameters
     *
     * @param Varien_Data_Form_Element_Abstract $element
     * @return string
     */
    public function render(Varien_Data_Form_Element_Abstract $element)
    {
        $element->unsScope()->unsCanUseWebsiteValue()->unsCanUseDefaultValue();
        return parent::render($element);
    }

    /**
     * Get the AJAX handler HTML
     *
     * @param Varien_Data_Form_Element_Abstract $element
     * @return string
     */
    protected function _getElementHtml(Varien_Data_Form_Element_Abstract $element)
    {
        $this->addData(array(
            'html_id' => $element->getHtmlId(),
        ));

        return $this->_toHtml();
    }
    
    /**
     * Get helper
     *
     * @return PFG_Cloudflare_Helper_Data
     */
    public function getHelper()
    {
        return Mage::helper('pfg_cloudflare');
    }
}

<?php
/**
 * PFG Cloudflare Integration Module
 * 
 * @category   PFG
 * @package    PFG_Cloudflare
 * <AUTHOR> Development Team
 */

class PFG_Cloudflare_Block_Adminhtml_System_Config_Connectionstatus extends Mage_Adminhtml_Block_System_Config_Form_Field
{
    /**
     * Set template to itself
     */
    protected function _prepareLayout()
    {
        parent::_prepareLayout();
        if (!$this->getTemplate()) {
            $this->setTemplate('pfg_cloudflare/system/config/connectionstatus.phtml');
        }
        return $this;
    }

    /**
     * Unset some non-related element parameters
     *
     * @param Varien_Data_Form_Element_Abstract $element
     * @return string
     */
    public function render(Varien_Data_Form_Element_Abstract $element)
    {
        $element->unsScope()->unsCanUseWebsiteValue()->unsCanUseDefaultValue();
        return parent::render($element);
    }

    /**
     * Get the connection status content
     *
     * @param Varien_Data_Form_Element_Abstract $element
     * @return string
     */
    protected function _getElementHtml(Varien_Data_Form_Element_Abstract $element)
    {
        $this->addData(array(
            'html_id' => $element->getHtmlId(),
        ));

        return $this->_toHtml();
    }
    
    /**
     * Get helper
     *
     * @return PFG_Cloudflare_Helper_Data
     */
    public function getHelper()
    {
        return Mage::helper('pfg_cloudflare');
    }
    
    /**
     * Check if API is configured
     *
     * @return bool
     */
    public function isApiConfigured()
    {
        return $this->getHelper()->isApiConfigured();
    }
    
    /**
     * Get connection status
     *
     * @return array
     */
    public function getConnectionStatus()
    {
        if (!$this->isApiConfigured()) {
            return array(
                'status' => 'not_configured',
                'message' => 'API credentials are not configured.',
                'class' => 'error'
            );
        }
        
        try {
            $apiModel = Mage::getModel('pfg_cloudflare/api');
            $result = $apiModel->validateCredentials();
            
            if ($result['success']) {
                return array(
                    'status' => 'connected',
                    'message' => 'Connected to Cloudflare API successfully.',
                    'class' => 'success'
                );
            } else {
                return array(
                    'status' => 'error',
                    'message' => $result['message'],
                    'class' => 'error'
                );
            }
        } catch (Exception $e) {
            return array(
                'status' => 'error',
                'message' => 'Connection failed: ' . $e->getMessage(),
                'class' => 'error'
            );
        }
    }
    
    /**
     * Get zone information
     *
     * @return array|null
     */
    public function getZoneInfo()
    {
        if (!$this->isApiConfigured()) {
            return null;
        }
        
        try {
            $apiModel = Mage::getModel('pfg_cloudflare/api');
            $result = $apiModel->getZoneInfo();
            
            if ($result['success']) {
                return $result['data'];
            }
        } catch (Exception $e) {
            $this->getHelper()->log('Failed to get zone info: ' . $e->getMessage(), 'error');
        }
        
        return null;
    }
    
    /**
     * Format date
     *
     * @param string $date
     * @return string
     */
    public function formatDate($date)
    {
        return Mage::helper('core')->formatDate($date, 'medium', true);
    }
}

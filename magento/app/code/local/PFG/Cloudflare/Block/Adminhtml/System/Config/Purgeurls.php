<?php
/**
 * PFG Cloudflare Integration Module
 * 
 * @category   PFG
 * @package    PFG_Cloudflare
 * <AUTHOR> Development Team
 */

class PFG_Cloudflare_Block_Adminhtml_System_Config_Purgeurls extends Mage_Adminhtml_Block_System_Config_Form_Field
{
    /**
     * Get the button and scripts contents
     *
     * @param Varien_Data_Form_Element_Abstract $element
     * @return string
     */
    protected function _getElementHtml(Varien_Data_Form_Element_Abstract $element)
    {
        $html = parent::_getElementHtml($element);
        
        // Add purge button
        $buttonHtml = '<br/><button id="' . $element->getHtmlId() . '_button" title="' . $this->__('Purge URLs') . '" type="button" class="scalable" onclick="purgeUrlsCloudflareCache(\'' . $element->getHtmlId() . '\'); return false;">';
        $buttonHtml .= '<span><span><span>' . $this->__('Purge URLs') . '</span></span></span>';
        $buttonHtml .= '</button>';
        
        $html .= $buttonHtml;
        $html .= $this->_getPurgeUrlsScript($element->getHtmlId());
        
        return $html;
    }
    
    /**
     * Get purge URLs JavaScript
     *
     * @param string $elementId
     * @return string
     */
    protected function _getPurgeUrlsScript($elementId)
    {
        $ajaxUrl = Mage::helper('adminhtml')->getUrl('pfg_cloudflare/adminhtml_system/purgeUrlsCache');
        
        return "
        <script type=\"text/javascript\">
        //<![CDATA[
        function purgeUrlsCloudflareCache(elementId) {
            // Find the actual textarea field for URLs
            var textarea = $('pfg_cloudflare_cache_management_purge_urls');
            var button = $(elementId + '_button');
            
            if (!textarea || !button) {
                alert('" . $this->__('Error: Could not find form elements.') . "');
                return;
            }
            
            var urls = textarea.value.trim();
            if (!urls) {
                alert('" . $this->__('Please enter URLs to purge (comma or line separated).') . "');
                return;
            }
            
            // Count URLs - handle both comma and line separation
            var urlArray = urls.split(/[,\\n]/).map(function(url) {
                return url.trim();
            }).filter(function(url) {
                return url !== '';
            });
            
            if (urlArray.length > 30) {
                alert('" . $this->__('Maximum 30 URLs allowed per request.') . "');
                return;
            }
            
            if (!confirm('" . $this->__('Are you sure you want to purge cache for these URLs?') . "')) {
                return;
            }
            
            var originalText = button.innerHTML;
            
            // Disable button and show loading state
            button.disabled = true;
            button.innerHTML = '<span><span><span>" . $this->__('Purging...') . "</span></span></span>';
            
            // Make AJAX request
            new Ajax.Request('{$ajaxUrl}', {
                method: 'post',
                parameters: {
                    urls: urls,
                    form_key: FORM_KEY
                },
                onSuccess: function(response) {
                    button.disabled = false;
                    button.innerHTML = originalText;
                    
                    try {
                        var result = response.responseText.evalJSON();
                        if (result.success) {
                            alert('" . $this->__('Success: ') . "' + result.message);
                            textarea.value = ''; // Clear textarea on success
                        } else {
                            alert('" . $this->__('Error: ') . "' + result.message);
                        }
                    } catch (e) {
                        alert('" . $this->__('Error: Invalid response from server.') . "');
                    }
                },
                onFailure: function() {
                    button.disabled = false;
                    button.innerHTML = originalText;
                    alert('" . $this->__('Error: URL cache purge failed. Please try again.') . "');
                }
            });
        }
        //]]>
        </script>";
    }
}

<?php
/**
 * PFG Cloudflare Integration Module
 * 
 * @category   PFG
 * @package    PFG_Cloudflare
 * <AUTHOR> Development Team
 */

class PFG_Cloudflare_Block_Adminhtml_System_Config_Currentsettings extends Mage_Adminhtml_Block_System_Config_Form_Field
{
    /**
     * Set template to itself
     */
    protected function _prepareLayout()
    {
        parent::_prepareLayout();
        if (!$this->getTemplate()) {
            $this->setTemplate('pfg_cloudflare/system/config/currentsettings.phtml');
        }
        return $this;
    }

    /**
     * Unset some non-related element parameters
     *
     * @param Varien_Data_Form_Element_Abstract $element
     * @return string
     */
    public function render(Varien_Data_Form_Element_Abstract $element)
    {
        $element->unsScope()->unsCanUseWebsiteValue()->unsCanUseDefaultValue();
        return parent::render($element);
    }

    /**
     * Get the current settings display HTML
     *
     * @param Varien_Data_Form_Element_Abstract $element
     * @return string
     */
    protected function _getElementHtml(Varien_Data_Form_Element_Abstract $element)
    {
        $this->addData(array(
            'html_id' => $element->getHtmlId(),
        ));

        return $this->_toHtml();
    }
    
    /**
     * Get helper
     *
     * @return PFG_Cloudflare_Helper_Data
     */
    public function getHelper()
    {
        return Mage::helper('pfg_cloudflare');
    }
    
    /**
     * Check if API is configured
     *
     * @return bool
     */
    public function isApiConfigured()
    {
        return $this->getHelper()->isApiConfigured();
    }
    
    /**
     * Get current CloudFlare settings
     *
     * @return array|null
     */
    public function getCurrentSettings()
    {
        if (!$this->isApiConfigured()) {
            return null;
        }
        
        try {
            $apiModel = Mage::getModel('pfg_cloudflare/api');
            
            // Get specific settings we want to display
            $settingsToFetch = array(
                'security_level',
                'ssl',
                'cache_level',
                'browser_cache_ttl',
                'development_mode',
                'minify',
                'rocket_loader',
                'polish',
                'brotli'
            );
            
            $currentSettings = array();
            foreach ($settingsToFetch as $setting) {
                $result = $apiModel->getZoneSetting($setting);
                if ($result['success'] && isset($result['data']['value'])) {
                    $currentSettings[$setting] = $result['data']['value'];
                }
            }
            
            return $currentSettings;
        } catch (Exception $e) {
            $this->getHelper()->log('Failed to get current settings: ' . $e->getMessage(), 'error');
            return null;
        }
    }
    
    /**
     * Format setting value for display
     *
     * @param string $setting
     * @param mixed $value
     * @return string
     */
    public function formatSettingValue($setting, $value)
    {
        switch ($setting) {
            case 'security_level':
                return ucwords(str_replace('_', ' ', $value));
            case 'ssl':
                return ucwords(str_replace('_', ' ', $value));
            case 'cache_level':
                return ucwords(str_replace('_', ' ', $value));
            case 'browser_cache_ttl':
                return $this->formatTtl($value);
            case 'development_mode':
            case 'rocket_loader':
            case 'brotli':
                return $value === 'on' || $value === true ? 'ON' : 'OFF';
            case 'minify':
                if (is_array($value)) {
                    return implode(', ', array_map('strtoupper', $value));
                }
                return strtoupper($value);
            case 'polish':
                return ucwords(str_replace('_', ' ', $value));
            default:
                return is_array($value) ? implode(', ', $value) : (string)$value;
        }
    }
    
    /**
     * Format TTL value
     *
     * @param int $seconds
     * @return string
     */
    public function formatTtl($seconds)
    {
        if ($seconds < 3600) {
            return ($seconds / 60) . ' minutes';
        } elseif ($seconds < 86400) {
            return ($seconds / 3600) . ' hours';
        } else {
            return ($seconds / 86400) . ' days';
        }
    }
    
    /**
     * Get setting label
     *
     * @param string $setting
     * @return string
     */
    public function getSettingLabel($setting)
    {
        $labels = array(
            'security_level' => 'Security Level',
            'ssl' => 'SSL Mode',
            'cache_level' => 'Cache Level',
            'browser_cache_ttl' => 'Browser Cache TTL',
            'development_mode' => 'Development Mode',
            'minify' => 'Auto Minify',
            'rocket_loader' => 'Rocket Loader',
            'polish' => 'Polish',
            'brotli' => 'Brotli'
        );
        
        return isset($labels[$setting]) ? $this->__($labels[$setting]) : ucwords(str_replace('_', ' ', $setting));
    }
}

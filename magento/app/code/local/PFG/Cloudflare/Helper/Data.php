<?php
/**
 * PFG Cloudflare Integration Module
 * 
 * @category   PFG
 * @package    PFG_Cloudflare
 * <AUTHOR> Development Team
 */

class PFG_Cloudflare_Helper_Data extends Mage_Core_Helper_Abstract
{
    const XML_PATH_ENABLED = 'pfg_cloudflare/general/enabled';
    const XML_PATH_ACCOUNT_ID = 'pfg_cloudflare/general/account_id';
    const XML_PATH_ZONE_ID = 'pfg_cloudflare/general/zone_id';
    const XML_PATH_GLOBAL_API_KEY = 'pfg_cloudflare/general/global_api_key';
    const XML_PATH_EMAIL = 'pfg_cloudflare/general/email';
    const XML_PATH_API_CACHE_TTL = 'pfg_cloudflare/general/api_cache_ttl';
    const XML_PATH_API_TIMEOUT = 'pfg_cloudflare/general/api_timeout';

    const LOG_FILE = 'pfg_cloudflare.log';
    const API_BASE_URL = 'https://api.cloudflare.com/client/v4/';
    
    /**
     * Check if Cloudflare integration is enabled
     *
     * @param int|string|Mage_Core_Model_Store $store
     * @return bool
     */
    public function isEnabled($store = null)
    {
        return Mage::getStoreConfigFlag(self::XML_PATH_ENABLED, $store);
    }
    
    /**
     * Get Account ID
     *
     * @param int|string|Mage_Core_Model_Store $store
     * @return string
     */
    public function getAccountId($store = null)
    {
        return Mage::getStoreConfig(self::XML_PATH_ACCOUNT_ID, $store);
    }
    
    /**
     * Get Zone ID
     *
     * @param int|string|Mage_Core_Model_Store $store
     * @return string
     */
    public function getZoneId($store = null)
    {
        return Mage::getStoreConfig(self::XML_PATH_ZONE_ID, $store);
    }
    
    /**
     * Get Global API Key (decrypted)
     *
     * @param int|string|Mage_Core_Model_Store $store
     * @return string
     */
    public function getGlobalApiKey($store = null)
    {
        $encryptedKey = Mage::getStoreConfig(self::XML_PATH_GLOBAL_API_KEY, $store);
        return Mage::helper('core')->decrypt($encryptedKey);
    }
    
    /**
     * Get Email Address
     *
     * @param int|string|Mage_Core_Model_Store $store
     * @return string
     */
    public function getEmail($store = null)
    {
        return Mage::getStoreConfig(self::XML_PATH_EMAIL, $store);
    }
    
    /**
     * Get API Cache TTL
     *
     * @param int|string|Mage_Core_Model_Store $store
     * @return int
     */
    public function getApiCacheTtl($store = null)
    {
        return (int)Mage::getStoreConfig(self::XML_PATH_API_CACHE_TTL, $store);
    }

    /**
     * Get API timeout
     *
     * @param int|string|Mage_Core_Model_Store $store
     * @return int
     */
    public function getApiTimeout($store = null)
    {
        $timeout = (int) Mage::getStoreConfig(self::XML_PATH_API_TIMEOUT, $store);
        return $timeout > 0 ? $timeout : 30; // Default to 30 seconds
    }

    /**
     * Check if logging is enabled
     *
     * @param int|string|Mage_Core_Model_Store $store
     * @return bool
     */
    public function isLoggingEnabled($store = null)
    {
        // Logging is disabled by default for production
        return false;
    }

    /**
     * Get log level
     *
     * @param int|string|Mage_Core_Model_Store $store
     * @return string
     */
    public function getLogLevel($store = null)
    {
        return 'error';
    }
    
    /**
     * Check if API is properly configured
     *
     * @param int|string|Mage_Core_Model_Store $store
     * @return bool
     */
    public function isApiConfigured($store = null)
    {
        return $this->isEnabled($store) &&
               !empty($this->getAccountId($store)) &&
               !empty($this->getZoneId($store)) &&
               !empty($this->getGlobalApiKey($store)) &&
               !empty($this->getEmail($store));
    }
    
    /**
     * Log message to Cloudflare log file
     *
     * @param string $message
     * @param string $level
     * @param array $context
     * @return void
     */
    public function log($message, $level = 'info', $context = array())
    {
        if (!$this->isLoggingEnabled()) {
            return;
        }
        
        $logLevel = $this->getLogLevel();
        $allowedLevels = array('error', 'warning', 'info', 'debug');
        
        if (array_search($level, $allowedLevels) > array_search($logLevel, $allowedLevels)) {
            return;
        }
        
        $contextString = '';
        if (!empty($context)) {
            $contextString = ' | Context: ' . json_encode($context);
        }
        
        $logMessage = sprintf(
            '[%s] %s: %s%s',
            strtoupper($level),
            date('Y-m-d H:i:s'),
            $message,
            $contextString
        );
        
        Mage::log($logMessage, null, self::LOG_FILE);
    }
    
    /**
     * Sanitize input data
     *
     * @param mixed $data
     * @return mixed
     */
    public function sanitizeInput($data)
    {
        if (is_string($data)) {
            return trim(strip_tags($data));
        } elseif (is_array($data)) {
            return array_map(array($this, 'sanitizeInput'), $data);
        }
        
        return $data;
    }
    
    /**
     * Validate URL format
     *
     * @param string $url
     * @return bool
     */
    public function isValidUrl($url)
    {
        return filter_var($url, FILTER_VALIDATE_URL) !== false;
    }
    
    /**
     * Format bytes to human readable format
     *
     * @param int $bytes
     * @param int $precision
     * @return string
     */
    public function formatBytes($bytes, $precision = 2)
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
}

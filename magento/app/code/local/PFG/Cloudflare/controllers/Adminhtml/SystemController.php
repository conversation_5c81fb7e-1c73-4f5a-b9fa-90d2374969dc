<?php
/**
 * PFG Cloudflare Integration Module
 * 
 * @category   PFG
 * @package    PFG_Cloudflare
 * <AUTHOR> Development Team
 */

class PFG_Cloudflare_Adminhtml_SystemController extends Mage_Adminhtml_Controller_Action
{
    /**
     * Test Cloudflare API connection
     */
    public function testConnectionAction()
    {
        $result = array('success' => false, 'message' => '');
        
        try {
            // Validate form key for CSRF protection
            if (!$this->_validateFormKey()) {
                $result['message'] = 'Invalid form key. Please refresh the page and try again.';
                $this->getResponse()->setBody(Mage::helper('core')->jsonEncode($result));
                return;
            }
            
            $request = $this->getRequest();
            $accountId = trim($request->getParam('account_id', ''));
            $zoneId = trim($request->getParam('zone_id', ''));
            $globalApiKey = trim($request->getParam('global_api_key', ''));
            $email = trim($request->getParam('email', ''));

            // Validate required parameters
            if (empty($accountId) || empty($zoneId) || empty($globalApiKey) || empty($email)) {
                $result['message'] = 'All fields are required for connection testing.';
                $this->getResponse()->setBody(Mage::helper('core')->jsonEncode($result));
                return;
            }
            
            // Validate email format
            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                $result['message'] = 'Please enter a valid email address.';
                $this->getResponse()->setBody(Mage::helper('core')->jsonEncode($result));
                return;
            }
            
            // Test API connection using provided credentials
            $testResult = $this->_testApiConnection($email, $globalApiKey, $zoneId);
            
            if ($testResult['success']) {
                $result['success'] = true;
                $result['message'] = 'Connection successful! Your Cloudflare API credentials are valid.';
            } else {
                $result['message'] = $testResult['message'];
            }
            
        } catch (Exception $e) {
            $result['message'] = 'Connection test failed: ' . $e->getMessage();
        }
        
        $this->getResponse()->setBody(Mage::helper('core')->jsonEncode($result));
    }
    
    /**
     * Test API connection with provided credentials
     *
     * @param string $email
     * @param string $globalApiKey
     * @param string $zoneId
     * @return array
     */
    protected function _testApiConnection($email, $globalApiKey, $zoneId)
    {
        try {
            // Validate Global API Key format
            $globalApiKey = trim($globalApiKey); // Remove any whitespace
            $keyLength = strlen($globalApiKey);

            // Cloudflare Global API Keys can be 37 or 40+ characters depending on when they were generated
            if ($keyLength < 37 || $keyLength > 45) {
                return array(
                    'success' => false,
                    'message' => 'Invalid Global API Key format. Expected 37-45 characters, got ' . $keyLength . ' characters.'
                );
            }

            // Check if it matches the expected pattern (hexadecimal characters)
            if (!preg_match('/^[a-f0-9]+$/i', $globalApiKey)) {
                return array(
                    'success' => false,
                    'message' => 'Invalid Global API Key format. Must contain only hexadecimal characters (0-9, a-f).'
                );
            }
            // Test user endpoint first
            $userResult = $this->_makeTestApiRequest('user', 'GET', array(), $email, $globalApiKey);
            
            if (!$userResult['success']) {
                return array(
                    'success' => false,
                    'message' => 'Authentication failed: ' . implode(', ', $userResult['errors'])
                );
            }
            
            // Test zone access
            $zoneResult = $this->_makeTestApiRequest("zones/{$zoneId}", 'GET', array(), $email, $globalApiKey);
            
            if (!$zoneResult['success']) {
                return array(
                    'success' => false,
                    'message' => 'Zone access failed: ' . implode(', ', $zoneResult['errors'])
                );
            }
            
            return array(
                'success' => true,
                'message' => 'Connection successful! API credentials are valid and zone is accessible.'
            );
            
        } catch (Exception $e) {
            return array(
                'success' => false,
                'message' => 'Connection failed: ' . $e->getMessage()
            );
        }
    }
    
    /**
     * Make test API request to Cloudflare
     *
     * @param string $endpoint
     * @param string $method
     * @param array $data
     * @param string $email
     * @param string $globalApiKey
     * @return array
     */
    protected function _makeTestApiRequest($endpoint, $method = 'GET', $data = array(), $email, $globalApiKey)
    {
        $url = PFG_Cloudflare_Helper_Data::API_BASE_URL . $endpoint;

        $headers = array(
            'X-Auth-Email: ' . $email,
            'X-Auth-Key: ' . $globalApiKey,
            'Content-Type: application/json'
        );


        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
        
        switch (strtoupper($method)) {
            case 'POST':
                curl_setopt($ch, CURLOPT_POST, true);
                if (!empty($data)) {
                    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
                }
                break;
            case 'PATCH':
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PATCH');
                if (!empty($data)) {
                    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
                }
                break;
        }
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            throw new Exception('cURL Error: ' . $error);
        }
        
        if ($httpCode >= 400) {
            $decodedResponse = json_decode($response, true);
            if ($decodedResponse && isset($decodedResponse['errors']) && is_array($decodedResponse['errors'])) {
                $errorMessages = array();
                foreach ($decodedResponse['errors'] as $error) {
                    if (is_array($error) && isset($error['message'])) {
                        $errorMessages[] = $error['message'];
                    } elseif (is_string($error)) {
                        $errorMessages[] = $error;
                    } else {
                        $errorMessages[] = 'Unknown error';
                    }
                }
                throw new Exception('HTTP ' . $httpCode . ': ' . implode(', ', $errorMessages));
            } else {
                $errorMsg = 'Request failed';
                if ($decodedResponse && isset($decodedResponse['message'])) {
                    $errorMsg = $decodedResponse['message'];
                }
                throw new Exception('HTTP ' . $httpCode . ': ' . $errorMsg);
            }
        }
        
        $decodedResponse = json_decode($response, true);
        if (!$decodedResponse) {
            throw new Exception('Invalid JSON response from Cloudflare API');
        }
        
        return $decodedResponse;
    }

    /**
     * Update zone setting via AJAX
     */
    public function updateZoneSettingAction()
    {
        $result = array('success' => false, 'message' => '');

        try {
            // Validate form key for CSRF protection
            if (!$this->_validateFormKey()) {
                $result['message'] = 'Invalid form key. Please refresh the page and try again.';
                $this->getResponse()->setBody(Mage::helper('core')->jsonEncode($result));
                return;
            }

            $request = $this->getRequest();
            $action = $request->getParam('action', '');
            $settingId = trim($request->getParam('setting_id', ''));

            if (empty($settingId)) {
                $result['message'] = 'Setting ID is required.';
                $this->getResponse()->setBody(Mage::helper('core')->jsonEncode($result));
                return;
            }

            $helper = Mage::helper('pfg_cloudflare');

            // Check if API is configured
            if (!$helper->isApiConfigured()) {
                $result['message'] = 'Cloudflare API is not properly configured.';
                $this->getResponse()->setBody(Mage::helper('core')->jsonEncode($result));
                return;
            }

            $apiModel = Mage::getModel('pfg_cloudflare/api');

            if ($action === 'get') {
                // Get current zone setting value
                $response = $apiModel->getZoneSetting($settingId);
                if ($response['success']) {
                    $result['success'] = true;
                    $result['data'] = $response['data'];
                } else {
                    $result['message'] = $response['message'];
                }
            } elseif ($action === 'update') {
                // Update zone setting
                $value = $request->getParam('value', '');

                // Handle JSON values (for minify setting)
                if ($this->_isJson($value)) {
                    $value = Mage::helper('core')->jsonDecode($value);
                }

                $response = $apiModel->updateZoneSetting($settingId, $value);
                if ($response['success']) {
                    $result['success'] = true;
                    $result['message'] = $response['message'];
                    $result['data'] = $response['data'];
                } else {
                    $result['message'] = $response['message'];
                }
            } else {
                $result['message'] = 'Invalid action specified.';
            }

        } catch (Exception $e) {
            $result['message'] = 'Zone setting operation failed: ' . $e->getMessage();
        }

        $this->getResponse()->setBody(Mage::helper('core')->jsonEncode($result));
    }

    /**
     * Purge all cache via AJAX
     */
    public function purgeAllCacheAction()
    {
        $result = array('success' => false, 'message' => '');

        try {
            // Validate form key for CSRF protection
            if (!$this->_validateFormKey()) {
                $result['message'] = 'Invalid form key. Please refresh the page and try again.';
                $this->getResponse()->setBody(Mage::helper('core')->jsonEncode($result));
                return;
            }

            $helper = Mage::helper('pfg_cloudflare');

            // Check if API is configured
            if (!$helper->isApiConfigured()) {
                $result['message'] = 'Cloudflare API is not properly configured.';
                $this->getResponse()->setBody(Mage::helper('core')->jsonEncode($result));
                return;
            }

            $apiModel = Mage::getModel('pfg_cloudflare/api');
            $response = $apiModel->purgeAllCache();

            if ($response['success']) {
                $result['success'] = true;
                $result['message'] = $response['message'];
            } else {
                $result['message'] = $response['message'];
            }

        } catch (Exception $e) {
            $result['message'] = 'Cache purge failed: ' . $e->getMessage();
        }

        $this->getResponse()->setBody(Mage::helper('core')->jsonEncode($result));
    }

    /**
     * Purge URLs cache via AJAX
     */
    public function purgeUrlsCacheAction()
    {
        $result = array('success' => false, 'message' => '');

        try {
            // Validate form key for CSRF protection
            if (!$this->_validateFormKey()) {
                $result['message'] = 'Invalid form key. Please refresh the page and try again.';
                $this->getResponse()->setBody(Mage::helper('core')->jsonEncode($result));
                return;
            }

            $request = $this->getRequest();
            $urlsText = trim($request->getParam('urls', ''));

            if (empty($urlsText)) {
                $result['message'] = 'Please enter URLs to purge.';
                $this->getResponse()->setBody(Mage::helper('core')->jsonEncode($result));
                return;
            }

            // Parse URLs from textarea (one per line)
            $urls = array_filter(array_map('trim', explode("\n", $urlsText)));

            if (count($urls) > 30) {
                $result['message'] = 'Maximum 30 URLs allowed per request.';
                $this->getResponse()->setBody(Mage::helper('core')->jsonEncode($result));
                return;
            }

            $helper = Mage::helper('pfg_cloudflare');

            // Check if API is configured
            if (!$helper->isApiConfigured()) {
                $result['message'] = 'Cloudflare API is not properly configured.';
                $this->getResponse()->setBody(Mage::helper('core')->jsonEncode($result));
                return;
            }

            $apiModel = Mage::getModel('pfg_cloudflare/api');
            $response = $apiModel->purgeCacheByUrls($urls);

            if ($response['success']) {
                $result['success'] = true;
                $result['message'] = $response['message'];
            } else {
                $result['message'] = $response['message'];
            }

        } catch (Exception $e) {
            $result['message'] = 'URL cache purge failed: ' . $e->getMessage();
        }

        $this->getResponse()->setBody(Mage::helper('core')->jsonEncode($result));
    }

    /**
     * Check if string is valid JSON
     *
     * @param string $string
     * @return bool
     */
    protected function _isJson($string)
    {
        if (!is_string($string)) {
            return false;
        }

        json_decode($string);
        return (json_last_error() == JSON_ERROR_NONE);
    }

    /**
     * Check ACL permissions
     *
     * @return bool
     */
    protected function _isAllowed()
    {
        return Mage::getSingleton('admin/session')->isAllowed('pfg/cloudflare');
    }
}

<?xml version="1.0"?>
<config>
    <tabs>
        <pfg translate="label" module="pfg_cloudflare">
            <label>PFG</label>
            <sort_order>200</sort_order>
        </pfg>
    </tabs>

    <sections>
        <pfg_cloudflare translate="label" module="pfg_cloudflare">
            <label>Cloudflare Integration</label>
            <tab>pfg</tab>
            <frontend_type>text</frontend_type>
            <sort_order>100</sort_order>
            <show_in_default>1</show_in_default>
            <show_in_website>1</show_in_website>
            <show_in_store>1</show_in_store>

            <groups>
                <general translate="label" module="pfg_cloudflare">
                    <label>General Settings</label>
                    <frontend_type>text</frontend_type>
                    <sort_order>10</sort_order>
                    <show_in_default>1</show_in_default>
                    <show_in_website>1</show_in_website>
                    <show_in_store>1</show_in_store>

                    <fields>
                        <enabled translate="label comment" module="pfg_cloudflare">
                            <label>Enable Cloudflare Integration</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>10</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <comment>Enable or disable Cloudflare integration.</comment>
                        </enabled>

                        <account_id translate="label comment" module="pfg_cloudflare">
                            <label>Account ID</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>20</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <comment>Your Cloudflare Account ID (found in the right sidebar of your Cloudflare dashboard).</comment>
                            <depends><enabled>1</enabled></depends>
                        </account_id>

                        <zone_id translate="label comment" module="pfg_cloudflare">
                            <label>Zone ID</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>30</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <comment>Your Cloudflare Zone ID (found in the right sidebar of your domain's overview page).</comment>
                            <depends><enabled>1</enabled></depends>
                        </zone_id>

                        <global_api_key translate="label comment" module="pfg_cloudflare">
                            <label>Global API Key</label>
                            <frontend_type>password</frontend_type>
                            <backend_model>adminhtml/system_config_backend_encrypted</backend_model>
                            <sort_order>40</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <comment>Your Cloudflare Global API Key (found in My Profile > API Tokens > Global API Key).</comment>
                            <depends><enabled>1</enabled></depends>
                        </global_api_key>

                        <email translate="label comment" module="pfg_cloudflare">
                            <label>Email</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>50</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <comment>Your Cloudflare account email address.</comment>
                            <depends><enabled>1</enabled></depends>
                        </email>

                        <connect_cloudflare translate="label comment" module="pfg_cloudflare">
                            <label>Connect to CloudFlare</label>
                            <comment>Click to connect your store to CloudFlare</comment>
                            <frontend_type>button</frontend_type>
                            <frontend_model>pfg_cloudflare/adminhtml_system_config_testconnection</frontend_model>
                            <sort_order>60</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <depends><enabled>1</enabled></depends>
                        </connect_cloudflare>

                        <api_cache_ttl translate="label comment" module="pfg_cloudflare">
                            <label>API Cache TTL (seconds)</label>
                            <comment>How long to cache CloudFlare API responses (300 = 5 minutes). Set to 0 to disable caching.</comment>
                            <frontend_type>text</frontend_type>
                            <validate>validate-number validate-zero-or-greater</validate>
                            <sort_order>70</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <depends><enabled>1</enabled></depends>
                        </api_cache_ttl>

                        <api_timeout translate="label comment" module="pfg_cloudflare">
                            <label>API Timeout (seconds)</label>
                            <comment>Maximum time to wait for CloudFlare API responses (default: 30 seconds).</comment>
                            <frontend_type>text</frontend_type>
                            <validate>validate-number validate-greater-than-zero</validate>
                            <sort_order>80</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <depends><enabled>1</enabled></depends>
                        </api_timeout>

                        <!-- Two-Column Layout Container -->
                        <two_column_layout translate="label comment" module="pfg_cloudflare">
                            <label></label>
                            <comment></comment>
                            <frontend_type>text</frontend_type>
                            <frontend_model>pfg_cloudflare/adminhtml_system_config_twocolumnlayout</frontend_model>
                            <sort_order>90</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <depends><enabled>1</enabled></depends>
                        </two_column_layout>

                        <ajax_scripts translate="label comment" module="pfg_cloudflare">
                            <label></label>
                            <comment></comment>
                            <frontend_type>text</frontend_type>
                            <frontend_model>pfg_cloudflare/adminhtml_system_config_ajaxHandler</frontend_model>
                            <sort_order>999</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <depends><enabled>1</enabled></depends>
                        </ajax_scripts>
                    </fields>
                </general>


                <zone_settings translate="label" module="pfg_cloudflare">
                    <label>Zone Settings</label>
                    <frontend_type>text</frontend_type>
                    <sort_order>20</sort_order>
                    <show_in_default>1</show_in_default>
                    <show_in_website>1</show_in_website>
                    <show_in_store>1</show_in_store>
                    <depends><enabled>1</enabled></depends>

                    <fields>
                        <development_mode translate="label comment" module="pfg_cloudflare">
                            <label>Development Mode</label>
                            <frontend_type>text</frontend_type>
                            <frontend_model>pfg_cloudflare/adminhtml_system_config_toggle</frontend_model>
                            <sort_order>10</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <comment>Bypass Cloudflare's accelerated cache and slow down your site, but is useful for making changes to cacheable content (like images, css, or JavaScript) and seeing those changes right away. Leave this on only as long as necessary.</comment>
                        </development_mode>

                        <security_level translate="label comment" module="pfg_cloudflare">
                            <label>Security Level</label>
                            <frontend_type>select</frontend_type>
                            <source_model>pfg_cloudflare/system_config_source_securitylevel</source_model>
                            <sort_order>20</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <comment>Choose the appropriate security level for your website. Higher levels provide more protection but may block legitimate visitors.</comment>
                        </security_level>

                        <ssl translate="label comment" module="pfg_cloudflare">
                            <label>SSL</label>
                            <frontend_type>select</frontend_type>
                            <source_model>pfg_cloudflare/system_config_source_sslmode</source_model>
                            <sort_order>30</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <comment>SSL encrypts your connection to websites and protects your data from being intercepted by attackers.</comment>
                        </ssl>

                        <cache_level translate="label comment" module="pfg_cloudflare">
                            <label>Cache Level</label>
                            <frontend_type>select</frontend_type>
                            <source_model>pfg_cloudflare/system_config_source_cachelevel</source_model>
                            <sort_order>40</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <comment>Determine how much of your website's static content Cloudflare will cache.</comment>
                        </cache_level>

                        <browser_cache_ttl translate="label comment" module="pfg_cloudflare">
                            <label>Browser Cache TTL</label>
                            <frontend_type>select</frontend_type>
                            <source_model>pfg_cloudflare/system_config_source_browsercachettl</source_model>
                            <sort_order>50</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <comment>Determine the length of time Cloudflare instructs a visitor's browser to cache files.</comment>
                        </browser_cache_ttl>

                        <!-- Website Optimization Fields -->
                        <minify_css translate="label comment" module="pfg_cloudflare">
                            <label>Minify CSS</label>
                            <frontend_type>text</frontend_type>
                            <frontend_model>pfg_cloudflare/adminhtml_system_config_toggle</frontend_model>
                            <sort_order>60</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <comment>Reduce the file size of CSS files by removing unnecessary characters.</comment>
                        </minify_css>

                        <minify_html translate="label comment" module="pfg_cloudflare">
                            <label>Minify HTML</label>
                            <frontend_type>text</frontend_type>
                            <frontend_model>pfg_cloudflare/adminhtml_system_config_toggle</frontend_model>
                            <sort_order>61</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <comment>Reduce the file size of HTML files by removing unnecessary characters.</comment>
                        </minify_html>

                        <minify_js translate="label comment" module="pfg_cloudflare">
                            <label>Minify JavaScript</label>
                            <frontend_type>text</frontend_type>
                            <frontend_model>pfg_cloudflare/adminhtml_system_config_toggle</frontend_model>
                            <sort_order>62</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <comment>Reduce the file size of JavaScript files by removing unnecessary characters.</comment>
                        </minify_js>

                        <rocket_loader translate="label comment" module="pfg_cloudflare">
                            <label>Rocket Loader</label>
                            <frontend_type>text</frontend_type>
                            <frontend_model>pfg_cloudflare/adminhtml_system_config_toggle</frontend_model>
                            <sort_order>70</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <comment>Improve the paint time for pages which include JavaScript.</comment>
                        </rocket_loader>

                        <polish translate="label comment" module="pfg_cloudflare">
                            <label>Polish</label>
                            <frontend_type>select</frontend_type>
                            <source_model>pfg_cloudflare/system_config_source_polish</source_model>
                            <sort_order>80</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <comment>Reduce the file size of JPEG and PNG images through lossless or lossy compression.</comment>
                        </polish>

                        <brotli translate="label comment" module="pfg_cloudflare">
                            <label>Brotli</label>
                            <frontend_type>text</frontend_type>
                            <frontend_model>pfg_cloudflare/adminhtml_system_config_toggle</frontend_model>
                            <sort_order>90</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <comment>Speed up page load times for your visitor's HTTPS traffic by applying Brotli compression.</comment>
                        </brotli>

                        <!-- Two-Column Layout for Zone Settings -->
                        <zone_two_column_layout translate="label comment" module="pfg_cloudflare">
                            <label></label>
                            <comment></comment>
                            <frontend_type>text</frontend_type>
                            <frontend_model>pfg_cloudflare/adminhtml_system_config_zonetwocolumnlayout</frontend_model>
                            <sort_order>100</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </zone_two_column_layout>
                    </fields>
                </zone_settings>

                <cache_management translate="label" module="pfg_cloudflare">
                    <label>Cache Management</label>
                    <frontend_type>text</frontend_type>
                    <sort_order>40</sort_order>
                    <show_in_default>1</show_in_default>
                    <show_in_website>1</show_in_website>
                    <show_in_store>1</show_in_store>
                    <depends><enabled>1</enabled></depends>

                    <fields>
                        <purge_everything translate="label comment" module="pfg_cloudflare">
                            <label>Purge All Cache</label>
                            <comment>Clear all cached content from CloudFlare</comment>
                            <frontend_type>button</frontend_type>
                            <frontend_model>pfg_cloudflare/adminhtml_system_config_purgeeverything</frontend_model>
                            <sort_order>10</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </purge_everything>

                        <purge_urls translate="label comment" module="pfg_cloudflare">
                            <label>Purge Specific URLs</label>
                            <comment>Enter comma-separated URLs to purge from CloudFlare cache</comment>
                            <frontend_type>textarea</frontend_type>
                            <sort_order>20</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </purge_urls>

                        <purge_urls_button translate="label comment" module="pfg_cloudflare">
                            <label>Purge URLs</label>
                            <comment>Execute selective cache purge for the URLs specified above</comment>
                            <frontend_type>button</frontend_type>
                            <frontend_model>pfg_cloudflare/adminhtml_system_config_purgeurls</frontend_model>
                            <sort_order>30</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </purge_urls_button>
                    </fields>
                </cache_management>

                <api_settings translate="label" module="pfg_cloudflare">
                    <label>API Settings</label>
                    <frontend_type>text</frontend_type>
                    <sort_order>50</sort_order>
                    <show_in_default>1</show_in_default>
                    <show_in_website>1</show_in_website>
                    <show_in_store>1</show_in_store>
                    <depends><enabled>1</enabled></depends>

                    <fields>
                        <api_cache_ttl translate="label comment" module="pfg_cloudflare">
                            <label>API Cache TTL (seconds)</label>
                            <comment>How long to cache CloudFlare API responses (300 = 5 minutes). Set to 0 to disable caching.</comment>
                            <frontend_type>text</frontend_type>
                            <validate>validate-number validate-zero-or-greater</validate>
                            <sort_order>10</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </api_cache_ttl>

                        <api_timeout translate="label comment" module="pfg_cloudflare">
                            <label>API Timeout (seconds)</label>
                            <comment>Maximum time to wait for CloudFlare API responses (default: 30 seconds).</comment>
                            <frontend_type>text</frontend_type>
                            <validate>validate-number validate-greater-than-zero</validate>
                            <sort_order>20</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </api_timeout>
                    </fields>
                </api_settings>

            </groups>
        </pfg_cloudflare>
    </sections>
</config>

<?xml version="1.0"?>
<config>
    <modules>
        <PFG_Cloudflare>
            <version>1.0.0</version>
            <depends>
                <Mage_Core />
                <Mage_Adminhtml />
            </depends>
        </PFG_Cloudflare>
    </modules>
    
    <global>
        <models>
            <pfg_cloudflare>
                <class>PFG_Cloudflare_Model</class>
            </pfg_cloudflare>
        </models>

        <blocks>
            <pfg_cloudflare>
                <class>PFG_Cloudflare_Block</class>
            </pfg_cloudflare>
        </blocks>

        <helpers>
            <pfg_cloudflare>
                <class>PFG_Cloudflare_Helper</class>
            </pfg_cloudflare>
        </helpers>

        <fieldsets>
            <pfg_cloudflare_general>
                <label>PFG Cloudflare General</label>
            </pfg_cloudflare_general>
        </fieldsets>
    </global>

    <default>
        <pfg_cloudflare>
            <general>
                <enabled>0</enabled>
                <account_id></account_id>
                <zone_id></zone_id>
                <global_api_key></global_api_key>
                <email></email>
            </general>
            <zone_settings>
                <development_mode>0</development_mode>
                <security_level>medium</security_level>
                <ssl>flexible</ssl>
                <cache_level>aggressive</cache_level>
                <browser_cache_ttl>14400</browser_cache_ttl>
            </zone_settings>
            <cache_management>
                <auto_purge_enabled>0</auto_purge_enabled>
                <purge_on_product_save>0</purge_on_product_save>
                <purge_on_category_save>0</purge_on_category_save>
                <purge_on_cms_save>0</purge_on_cms_save>
            </cache_management>
            <optimization>
                <minify_css>0</minify_css>
                <minify_html>0</minify_html>
                <minify_js>0</minify_js>
                <rocket_loader>0</rocket_loader>
                <polish>off</polish>
                <brotli>0</brotli>
            </optimization>
            <cache>
                <api_cache_ttl>300</api_cache_ttl>
                <api_timeout>30</api_timeout>
            </cache>
        </pfg_cloudflare>
    </default>

    <admin>
        <routers>
            <pfg_cloudflare>
                <use>admin</use>
                <args>
                    <module>PFG_Cloudflare</module>
                    <frontName>pfg_cloudflare</frontName>
                </args>
            </pfg_cloudflare>
        </routers>
    </admin>
    
    <adminhtml>
        <translate>
            <modules>
                <PFG_Cloudflare>
                    <files>
                        <default>PFG_Cloudflare.csv</default>
                    </files>
                </PFG_Cloudflare>
            </modules>
        </translate>

        <menu>
            <pfg translate="title" module="pfg_cloudflare">
                <title>PFG</title>
                <sort_order>100</sort_order>
                <children>
                    <cloudflare translate="title" module="pfg_cloudflare">
                        <title>Cloudflare Integration</title>
                        <sort_order>10</sort_order>
                        <action>adminhtml/system_config/edit/section/pfg_cloudflare</action>
                    </cloudflare>
                </children>
            </pfg>
        </menu>
        
        <acl>
            <resources>
                <admin>
                    <children>
                        <pfg translate="title" module="pfg_cloudflare">
                            <title>PFG</title>
                            <sort_order>100</sort_order>
                            <children>
                                <cloudflare translate="title" module="pfg_cloudflare">
                                    <title>Cloudflare Integration</title>
                                    <sort_order>10</sort_order>
                                </cloudflare>
                            </children>
                        </pfg>
                        <system>
                            <children>
                                <config>
                                    <children>
                                        <pfg_cloudflare translate="title" module="pfg_cloudflare">
                                            <title>Cloudflare Integration</title>
                                        </pfg_cloudflare>
                                    </children>
                                </config>
                            </children>
                        </system>
                    </children>
                </admin>
            </resources>
        </acl>
        
        <layout>
            <updates>
                <pfg_cloudflare>
                    <file>pfg_cloudflare.xml</file>
                </pfg_cloudflare>
            </updates>
        </layout>
    </adminhtml>
</config>

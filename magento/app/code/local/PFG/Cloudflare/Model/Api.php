<?php
/**
 * PFG Cloudflare Integration Module
 * 
 * @category   PFG
 * @package    PFG_Cloudflare
 * <AUTHOR> Development Team
 */

class PFG_Cloudflare_Model_Api extends Mage_Core_Model_Abstract
{
    /**
     * @var PFG_Cloudflare_Helper_Data
     */
    protected $_helper;
    
    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->_helper = Mage::helper('pfg_cloudflare');
    }
    
    /**
     * Validate API credentials
     *
     * @return array
     */
    public function validateCredentials()
    {
        try {
            if (!$this->_helper->isApiConfigured()) {
                return array(
                    'success' => false,
                    'message' => 'API credentials are not properly configured.'
                );
            }
            
            $response = $this->_makeApiRequest('user', 'GET');
            
            if ($response['success']) {
                return array(
                    'success' => true,
                    'message' => 'Connection successful! API credentials are valid.',
                    'data' => $response['result']
                );
            } else {
                return array(
                    'success' => false,
                    'message' => 'Authentication failed: ' . implode(', ', $response['errors'])
                );
            }
        } catch (Exception $e) {
            return array(
                'success' => false,
                'message' => 'Connection failed: ' . $e->getMessage()
            );
        }
    }
    
    /**
     * Get zone information
     *
     * @return array
     */
    public function getZoneInfo()
    {
        try {
            $zoneId = $this->_helper->getZoneId();
            $response = $this->_makeApiRequest("zones/{$zoneId}", 'GET');
            
            if ($response['success']) {
                return array(
                    'success' => true,
                    'data' => $response['result']
                );
            } else {
                return array(
                    'success' => false,
                    'message' => 'Failed to retrieve zone information: ' . implode(', ', $response['errors'])
                );
            }
        } catch (Exception $e) {
            return array(
                'success' => false,
                'message' => 'Failed to retrieve zone information: ' . $e->getMessage()
            );
        }
    }
    
    /**
     * Get zone setting
     *
     * @param string $settingId
     * @return array
     */
    public function getZoneSetting($settingId)
    {
        try {
            $zoneId = $this->_helper->getZoneId();
            $response = $this->_makeApiRequest("zones/{$zoneId}/settings/{$settingId}", 'GET');
            
            if ($response['success']) {
                return array(
                    'success' => true,
                    'data' => $response['result']
                );
            } else {
                return array(
                    'success' => false,
                    'message' => 'Failed to retrieve zone setting: ' . implode(', ', $response['errors'])
                );
            }
        } catch (Exception $e) {
            return array(
                'success' => false,
                'message' => 'Failed to retrieve zone setting: ' . $e->getMessage()
            );
        }
    }
    
    /**
     * Update zone setting
     *
     * @param string $settingId
     * @param mixed $value
     * @return array
     */
    public function updateZoneSetting($settingId, $value)
    {
        try {
            $zoneId = $this->_helper->getZoneId();

            // Handle special cases for certain settings
            if ($settingId === 'minify_css' || $settingId === 'minify_html' || $settingId === 'minify_js') {
                // Individual minify settings need to update the main minify setting
                // First get current minify settings
                $currentMinify = $this->getZoneSetting('minify');
                $minifyOptions = array('css' => 'off', 'html' => 'off', 'js' => 'off');

                if ($currentMinify['success'] && isset($currentMinify['data']['value'])) {
                    $minifyOptions = array_merge($minifyOptions, $currentMinify['data']['value']);
                }

                // Update the specific minify type
                $minifyType = str_replace('minify_', '', $settingId);
                $minifyOptions[$minifyType] = ($value == '1' || $value === 'on') ? 'on' : 'off';

                // Update the main minify setting
                $settingId = 'minify';
                $data = array('value' => $minifyOptions);
            } elseif ($settingId === 'rocket_loader') {
                // Rocket Loader expects 'on' or 'off'
                $data = array('value' => ($value == '1' || $value === 'on') ? 'on' : 'off');
            } elseif ($settingId === 'brotli') {
                // Brotli expects 'on' or 'off'
                $data = array('value' => ($value == '1' || $value === 'on') ? 'on' : 'off');
            } elseif ($settingId === 'development_mode') {
                // Development mode expects 'on' or 'off'
                $data = array('value' => ($value == '1' || $value === 'on') ? 'on' : 'off');
            } else {
                $data = array('value' => $value);
            }

            $response = $this->_makeApiRequest("zones/{$zoneId}/settings/{$settingId}", 'PATCH', $data);

            if ($response['success']) {
                return array(
                    'success' => true,
                    'message' => 'Zone setting updated successfully.',
                    'data' => $response['result']
                );
            } else {
                return array(
                    'success' => false,
                    'message' => 'Failed to update zone setting: ' . implode(', ', $response['errors'])
                );
            }
        } catch (Exception $e) {
            return array(
                'success' => false,
                'message' => 'Failed to update zone setting: ' . $e->getMessage()
            );
        }
    }
    
    /**
     * Purge all cache
     *
     * @return array
     */
    public function purgeAllCache()
    {
        try {
            $zoneId = $this->_helper->getZoneId();
            $data = array('purge_everything' => true);
            
            $response = $this->_makeApiRequest("zones/{$zoneId}/purge_cache", 'POST', $data);
            
            if ($response['success']) {
                return array(
                    'success' => true,
                    'message' => 'All cache purged successfully.'
                );
            } else {
                return array(
                    'success' => false,
                    'message' => 'Failed to purge cache: ' . implode(', ', $response['errors'])
                );
            }
        } catch (Exception $e) {
            return array(
                'success' => false,
                'message' => 'Failed to purge cache: ' . $e->getMessage()
            );
        }
    }
    
    /**
     * Purge cache by URLs
     *
     * @param array $urls
     * @return array
     */
    public function purgeCacheByUrls($urls)
    {
        try {
            if (empty($urls) || !is_array($urls)) {
                return array(
                    'success' => false,
                    'message' => 'No valid URLs provided for cache purging.'
                );
            }
            
            // Validate URLs
            $validUrls = array();
            foreach ($urls as $url) {
                $url = trim($url);
                if ($this->_helper->isValidUrl($url)) {
                    $validUrls[] = $url;
                }
            }
            
            if (empty($validUrls)) {
                return array(
                    'success' => false,
                    'message' => 'No valid URLs found for cache purging.'
                );
            }
            
            $zoneId = $this->_helper->getZoneId();
            $data = array('files' => $validUrls);
            
            $response = $this->_makeApiRequest("zones/{$zoneId}/purge_cache", 'POST', $data);
            
            if ($response['success']) {
                return array(
                    'success' => true,
                    'message' => 'Cache purged successfully for ' . count($validUrls) . ' URL(s).'
                );
            } else {
                return array(
                    'success' => false,
                    'message' => 'Failed to purge cache: ' . implode(', ', $response['errors'])
                );
            }
        } catch (Exception $e) {
            return array(
                'success' => false,
                'message' => 'Failed to purge cache: ' . $e->getMessage()
            );
        }
    }
    
    /**
     * Make API request to Cloudflare
     *
     * @param string $endpoint
     * @param string $method
     * @param array $data
     * @return array
     */
    protected function _makeApiRequest($endpoint, $method = 'GET', $data = array())
    {
        $url = $this->_helper::API_BASE_URL . $endpoint;
        $email = $this->_helper->getEmail();
        $apiKey = $this->_helper->getGlobalApiKey();
        
        $headers = array(
            'X-Auth-Email: ' . $email,
            'X-Auth-Key: ' . $apiKey,
            'Content-Type: application/json'
        );
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_TIMEOUT, $this->_helper->getApiTimeout());
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
        
        switch (strtoupper($method)) {
            case 'POST':
                curl_setopt($ch, CURLOPT_POST, true);
                if (!empty($data)) {
                    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
                }
                break;
            case 'PATCH':
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PATCH');
                if (!empty($data)) {
                    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
                }
                break;
            case 'PUT':
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
                if (!empty($data)) {
                    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
                }
                break;
            case 'DELETE':
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
                break;
        }
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            throw new Exception('cURL Error: ' . $error);
        }
        
        if ($httpCode >= 400) {
            $decodedResponse = json_decode($response, true);
            if ($decodedResponse && isset($decodedResponse['errors'])) {
                throw new Exception('HTTP ' . $httpCode . ': ' . implode(', ', $decodedResponse['errors']));
            } else {
                throw new Exception('HTTP ' . $httpCode . ': Request failed');
            }
        }
        
        $decodedResponse = json_decode($response, true);
        if (!$decodedResponse) {
            throw new Exception('Invalid JSON response from Cloudflare API');
        }
        
        return $decodedResponse;
    }

    /**
     * Get multiple zone settings
     *
     * @param array $settingIds
     * @return array
     */
    public function getZoneSettings($settingIds = array())
    {
        try {
            $zoneId = $this->_helper->getZoneId();
            $response = $this->_makeApiRequest("zones/{$zoneId}/settings", 'GET');

            if ($response['success']) {
                $settings = array();
                foreach ($response['result'] as $setting) {
                    if (empty($settingIds) || in_array($setting['id'], $settingIds)) {
                        $settings[$setting['id']] = $setting;
                    }
                }

                return array(
                    'success' => true,
                    'data' => $settings
                );
            } else {
                return array(
                    'success' => false,
                    'message' => 'Failed to retrieve zone settings: ' . implode(', ', $response['errors'])
                );
            }
        } catch (Exception $e) {
            return array(
                'success' => false,
                'message' => 'Failed to retrieve zone settings: ' . $e->getMessage()
            );
        }
    }

    /**
     * Update multiple zone settings
     *
     * @param array $settings Array of setting_id => value pairs
     * @return array
     */
    public function updateZoneSettings($settings)
    {
        $results = array();
        $errors = array();

        foreach ($settings as $settingId => $value) {
            $result = $this->updateZoneSetting($settingId, $value);
            $results[$settingId] = $result;

            if (!$result['success']) {
                $errors[] = "Failed to update {$settingId}: " . $result['message'];
            }
        }

        if (empty($errors)) {
            return array(
                'success' => true,
                'message' => 'All zone settings updated successfully.',
                'data' => $results
            );
        } else {
            return array(
                'success' => false,
                'message' => implode('; ', $errors),
                'data' => $results
            );
        }
    }


}

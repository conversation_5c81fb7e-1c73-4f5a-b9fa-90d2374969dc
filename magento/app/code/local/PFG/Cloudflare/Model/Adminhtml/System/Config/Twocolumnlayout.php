<?php
/**
 * PFG CloudFlare Integration
 * 
 * Two-Column Layout Frontend Model
 * Renders a responsive two-column layout for General Settings
 * Left column: Configuration fields, Right column: Connection Status
 */

class PFG_Cloudflare_Model_Adminhtml_System_Config_Twocolumnlayout extends Mage_Adminhtml_Model_System_Config_Form_Field
{
    /**
     * Render the two-column layout
     *
     * @param Varien_Data_Form_Element_Abstract $element
     * @return string
     */
    protected function _getElementHtml(Varien_Data_Form_Element_Abstract $element)
    {
        // Get connection status HTML
        $connectionStatusModel = Mage::getModel('pfg_cloudflare/adminhtml_system_config_connectionstatus');
        $connectionStatusElement = new Varien_Data_Form_Element_Text();
        $connectionStatusElement->setForm($element->getForm());
        $connectionStatusElement->setData($element->getData());
        $connectionStatusHtml = $connectionStatusModel->_getElementHtml($connectionStatusElement);

        $html = '
        <tr id="pfg-cloudflare-two-column-layout">
            <td class="label"></td>
            <td class="value">
                <div class="pfg-two-column-container">
                    <div class="pfg-column-right">
                        <div class="pfg-connection-status-container">
                            <h4>Connection Status</h4>
                            ' . $connectionStatusHtml . '
                        </div>
                    </div>
                </div>
            </td>
        </tr>
        
        <style type="text/css">
        /* Two-Column Layout Styles */
        .pfg-two-column-container {
            display: flex;
            gap: 20px;
            width: 100%;
            margin-top: 20px;
        }
        
        .pfg-column-left {
            flex: 1;
            min-width: 0;
        }
        
        .pfg-column-right {
            flex: 1;
            min-width: 0;
        }
        
        .pfg-connection-status-container {
            background: #f8f8f8;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
        }
        
        .pfg-connection-status-container h4 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 13px;
            font-weight: bold;
        }
        
        /* Responsive behavior */
        @media (max-width: 768px) {
            .pfg-two-column-container {
                flex-direction: column;
            }
        }
        
        /* Hide the empty row that would be created by this field */
        #pfg-cloudflare-two-column-layout .label,
        #pfg-cloudflare-two-column-layout .value {
            padding: 0;
            border: none;
        }
        
        #pfg-cloudflare-two-column-layout {
            background: none;
        }
        </style>';

        return $html;
    }
}

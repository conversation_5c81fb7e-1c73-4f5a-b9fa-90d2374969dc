<?php
/**
 * CloudFlare AJAX Settings Handler
 * This template provides AJAX functionality for all CloudFlare settings
 */
?>

<script type="text/javascript">
//<![CDATA[
document.observe('dom:loaded', function() {
    // CloudFlare settings that should sync via AJAX
    var cloudflareSettings = {
        'pfg_cloudflare_zone_settings_development_mode': 'development_mode',
        'pfg_cloudflare_zone_settings_security_level': 'security_level',
        'pfg_cloudflare_zone_settings_ssl': 'ssl',
        'pfg_cloudflare_zone_settings_cache_level': 'cache_level',
        'pfg_cloudflare_zone_settings_browser_cache_ttl': 'browser_cache_ttl',
        'pfg_cloudflare_optimization_minify_css': 'minify_css',
        'pfg_cloudflare_optimization_minify_html': 'minify_html',
        'pfg_cloudflare_optimization_minify_js': 'minify_js',
        'pfg_cloudflare_optimization_rocket_loader': 'rocket_loader',
        'pfg_cloudflare_optimization_polish': 'polish',
        'pfg_cloudflare_optimization_brotli': 'brotli'
    };
    
    var ajaxUrl = '<?php echo Mage::helper('adminhtml')->getUrl('pfg_cloudflare/adminhtml_system/updateZoneSetting') ?>';
    var formKey = '<?php echo Mage::getSingleton('core/session')->getFormKey() ?>';
    
    // Load all current values from CloudFlare in a single batch request
    loadAllCurrentValues();

    // Add event listeners to all CloudFlare settings
    Object.keys(cloudflareSettings).forEach(function(elementId) {
        var element = $(elementId);
        if (element) {
            var settingId = cloudflareSettings[elementId];

            // Add change event listener
            if (element.type === 'checkbox' || element.classList.contains('pfg-toggle-input')) {
                // Handle toggle switches and checkboxes
                element.observe('change', function() {
                    var value = this.checked ? 'on' : 'off';
                    updateCloudFlareSetting(elementId, settingId, value);
                });
            } else if (element.tagName === 'SELECT') {
                // Handle dropdowns
                element.observe('change', function() {
                    updateCloudFlareSetting(elementId, settingId, this.value);
                });
            } else {
                // Handle text inputs
                element.observe('change', function() {
                    updateCloudFlareSetting(elementId, settingId, this.value);
                });
            }
        }
    });

    /**
     * Load all current values from CloudFlare in a single batch request
     */
    window.loadAllCurrentValues = function() {
        var cloudflareSettingsGlobal = {
            'pfg_cloudflare_zone_settings_development_mode': 'development_mode',
            'pfg_cloudflare_zone_settings_security_level': 'security_level',
            'pfg_cloudflare_zone_settings_ssl': 'ssl',
            'pfg_cloudflare_zone_settings_cache_level': 'cache_level',
            'pfg_cloudflare_zone_settings_browser_cache_ttl': 'browser_cache_ttl',
            'pfg_cloudflare_optimization_minify_css': 'minify_css',
            'pfg_cloudflare_optimization_minify_html': 'minify_html',
            'pfg_cloudflare_optimization_minify_js': 'minify_js',
            'pfg_cloudflare_optimization_rocket_loader': 'rocket_loader',
            'pfg_cloudflare_optimization_polish': 'polish',
            'pfg_cloudflare_optimization_brotli': 'brotli'
        };
        var settingIds = Object.values(cloudflareSettingsGlobal);

        new Ajax.Request('<?php echo Mage::helper('adminhtml')->getUrl('pfg_cloudflare/adminhtml_system/updateZoneSetting') ?>', {
            method: 'post',
            parameters: {
                action: 'get_all',
                setting_ids: settingIds.join(','),
                form_key: '<?php echo Mage::getSingleton('core/session')->getFormKey() ?>'
            },
            onSuccess: function(response) {
                try {
                    var result = response.responseText.evalJSON();
                    if (result.success && result.data) {
                        // Update all elements with their current values
                        Object.keys(cloudflareSettingsGlobal).forEach(function(elementId) {
                            var settingId = cloudflareSettingsGlobal[elementId];
                            var element = $(elementId);
                            if (element && result.data[settingId] !== undefined) {
                                updateElementValue(element, settingId, result.data[settingId]);
                            }
                        });
                    }
                } catch (e) {
                    // Silently handle parsing errors
                }
            },
            onFailure: function() {
                // Silently handle request failures
            }
        });
    };

    /**
     * Load current value from CloudFlare (kept for backward compatibility)
     */
    function loadCurrentValue(elementId, settingId) {
        new Ajax.Request(ajaxUrl, {
            method: 'post',
            parameters: {
                action: 'get',
                setting_id: settingId,
                form_key: formKey
            },
            onSuccess: function(response) {
                try {
                    var result = response.responseText.evalJSON();
                    if (result.success && result.data && result.data.value !== undefined) {
                        var element = $(elementId);
                        if (element) {
                            updateElementValue(element, settingId, result.data.value);
                        }
                    }
                } catch (e) {
                    // Silently handle parsing errors
                }
            },
            onFailure: function() {
                // Silently handle request failures
            }
        });
    }
    
    /**
     * Update CloudFlare setting via AJAX
     */
    function updateCloudFlareSetting(elementId, settingId, value) {
        var element = $(elementId);
        if (!element) return;
        
        // Show loading state
        showLoadingState(element);
        
        // Prepare value based on setting type
        var apiValue = prepareApiValue(settingId, value, element);
        
        new Ajax.Request(ajaxUrl, {
            method: 'post',
            parameters: {
                action: 'update',
                setting_id: settingId,
                value: Object.isString(apiValue) ? apiValue : Object.toJSON(apiValue),
                form_key: formKey
            },
            onSuccess: function(response) {
                try {
                    var result = response.responseText.evalJSON();
                    if (result.success) {
                        showSuccessState(element, result.message || 'Setting updated successfully');
                    } else {
                        showErrorState(element, result.message || 'Failed to update setting');
                    }
                } catch (e) {
                    showErrorState(element, 'Error processing response');
                }
            },
            onFailure: function() {
                showErrorState(element, 'Network error - please try again');
            }
        });
    }
    
    /**
     * Prepare API value based on setting type
     */
    function prepareApiValue(settingId, value, element) {
        switch (settingId) {
            case 'minify_css':
            case 'minify_html':
            case 'minify_js':
                // Handle individual minify toggles
                return value === '1' ? 'on' : 'off';
                
            case 'development_mode':
            case 'rocket_loader':
            case 'brotli':
                // Convert boolean/toggle values
                if (value === true || value === 'on' || value === '1') {
                    return 'on';
                } else {
                    return 'off';
                }
                
            case 'browser_cache_ttl':
                // Ensure numeric value
                return parseInt(value) || 14400;
                
            default:
                return value;
        }
    }
    
    /**
     * Update element value from CloudFlare response
     */
    function updateElementValue(element, settingId, value) {
        switch (settingId) {
            case 'minify_css':
            case 'minify_html':
            case 'minify_js':
                // Handle individual minify toggles
                element.value = value === 'on' ? '1' : '0';
                // Update toggle display if it exists
                var toggleElement = element.parentNode.querySelector('.pfg-toggle');
                if (toggleElement) {
                    toggleElement.className = 'pfg-toggle ' + (value === 'on' ? 'on' : 'off');
                }
                break;
                
            case 'development_mode':
            case 'rocket_loader':
            case 'brotli':
                // Handle toggle switches
                if (element.type === 'checkbox' || element.classList.contains('pfg-toggle-input')) {
                    element.checked = (value === 'on' || value === true);
                    // Trigger visual update for custom toggles
                    if (element.classList.contains('pfg-toggle-input')) {
                        var event = document.createEvent('HTMLEvents');
                        event.initEvent('change', true, false);
                        element.dispatchEvent(event);
                    }
                }
                break;
                
            default:
                // Handle regular selects and inputs
                if (element.tagName === 'SELECT') {
                    element.value = value;
                } else {
                    element.value = value;
                }
                break;
        }
    }
    
    /**
     * Show loading state
     */
    function showLoadingState(element) {
        element.style.backgroundColor = '#ffffcc';
        element.style.border = '1px solid #f0ad4e';
        if (element.nextSibling && element.nextSibling.className === 'cloudflare-status') {
            element.nextSibling.remove();
        }
    }
    
    /**
     * Show success state
     */
    function showSuccessState(element, message) {
        element.style.backgroundColor = '#dff0d8';
        element.style.border = '1px solid #5cb85c';
        
        var statusElement = document.createElement('span');
        statusElement.className = 'cloudflare-status success';
        statusElement.innerHTML = '✓ ' + message;
        statusElement.style.cssText = 'color: #5cb85c; font-size: 11px; margin-left: 5px;';
        
        if (element.nextSibling && element.nextSibling.className === 'cloudflare-status') {
            element.nextSibling.remove();
        }
        element.parentNode.insertBefore(statusElement, element.nextSibling);
        
        // Reset after 3 seconds
        setTimeout(function() {
            element.style.backgroundColor = '';
            element.style.border = '';
            if (statusElement.parentNode) {
                statusElement.remove();
            }
        }, 3000);
    }
    
    /**
     * Show error state
     */
    function showErrorState(element, message) {
        element.style.backgroundColor = '#f2dede';
        element.style.border = '1px solid #d9534f';
        
        var statusElement = document.createElement('span');
        statusElement.className = 'cloudflare-status error';
        statusElement.innerHTML = '✗ ' + message;
        statusElement.style.cssText = 'color: #d9534f; font-size: 11px; margin-left: 5px;';
        
        if (element.nextSibling && element.nextSibling.className === 'cloudflare-status') {
            element.nextSibling.remove();
        }
        element.parentNode.insertBefore(statusElement, element.nextSibling);
        
        // Reset after 5 seconds
        setTimeout(function() {
            element.style.backgroundColor = '';
            element.style.border = '';
            if (statusElement.parentNode) {
                statusElement.remove();
            }
        }, 5000);
    }
});

// Shared CloudFlare utility functions
window.PFGCloudflare = window.PFGCloudflare || {};

/**
 * Purge all cache with visual feedback
 */
PFGCloudflare.purgeAllCache = function(options) {
    options = options || {};
    var confirmMessage = options.confirmMessage || 'Are you sure you want to purge all cache from Cloudflare? This action cannot be undone.';
    var successMessage = options.successMessage || 'All cache purged successfully';
    var errorMessage = options.errorMessage || 'Failed to purge cache';
    var url = options.url || '<?php echo Mage::helper('adminhtml')->getUrl('pfg_cloudflare/adminhtml_system/purgeAllCache') ?>';
    var button = options.button;
    var statusElement = options.statusElement;

    if (!confirm(confirmMessage)) {
        return;
    }

    // Show loading state
    if (button) {
        button.disabled = true;
        button.innerHTML = '<span>Purging...</span>';
    }

    if (statusElement) {
        statusElement.innerHTML = '';
        statusElement.className = 'pfg-cache-status loading';
    }

    new Ajax.Request(url, {
        method: 'post',
        parameters: {
            form_key: FORM_KEY
        },
        onSuccess: function(response) {
            try {
                var result = response.responseText.evalJSON();
                if (result.success) {
                    if (statusElement) {
                        PFGCloudflare.showStatus(statusElement, 'success', result.message || successMessage);
                    } else {
                        alert('Success: ' + (result.message || successMessage));
                    }
                } else {
                    if (statusElement) {
                        PFGCloudflare.showStatus(statusElement, 'error', result.message || errorMessage);
                    } else {
                        alert('Error: ' + (result.message || errorMessage));
                    }
                }
            } catch (e) {
                if (statusElement) {
                    PFGCloudflare.showStatus(statusElement, 'error', 'Error processing response');
                } else {
                    alert('Error: Invalid response from server.');
                }
            }

            // Reset button
            if (button) {
                button.disabled = false;
                button.innerHTML = '<span>Purge Everything</span>';
            }
        },
        onFailure: function() {
            if (statusElement) {
                PFGCloudflare.showStatus(statusElement, 'error', 'Network error - please try again');
            } else {
                alert('Error: Cache purge failed. Please try again.');
            }

            // Reset button
            if (button) {
                button.disabled = false;
                button.innerHTML = '<span>Purge Everything</span>';
            }
        }
    });
};

/**
 * Show status message with visual feedback
 */
PFGCloudflare.showStatus = function(element, type, message) {
    if (!element) return;

    element.className = 'pfg-cache-status ' + type;

    if (type === 'success') {
        element.innerHTML = '✓ ' + message;
    } else if (type === 'error') {
        element.innerHTML = '✗ ' + message;
    } else {
        element.innerHTML = message;
    }

    // Clear status after 5 seconds
    setTimeout(function() {
        element.innerHTML = '';
        element.className = 'pfg-cache-status';
    }, 5000);
};

/**
 * Refresh CloudFlare Settings Function (Global)
 */
window.refreshCloudFlareSettings = function() {
    var button = $('refresh-settings-btn');
    if (!button) return;

    // Show loading state
    button.addClassName('pfg-refresh-loading');
    var originalText = button.down('span').down('span').down('span').innerHTML;
    button.down('span').down('span').down('span').innerHTML = 'Refreshing...';

    // Clear any existing cache and reload all settings
    new Ajax.Request('<?php echo Mage::helper('adminhtml')->getUrl('pfg_cloudflare/adminhtml_system/updateZoneSetting') ?>', {
        method: 'post',
        parameters: {
            action: 'refresh_all',
            form_key: '<?php echo Mage::getSingleton('core/session')->getFormKey() ?>'
        },
        onSuccess: function(response) {
            try {
                var result = response.responseText.evalJSON();
                if (result.success) {
                    // Show success state
                    button.addClassName('pfg-refresh-success');
                    button.down('span').down('span').down('span').innerHTML = 'Refreshed!';

                    // Reload all current values from fresh API data
                    loadAllCurrentValues();

                    // Reset button after 2 seconds
                    setTimeout(function() {
                        button.removeClassName('pfg-refresh-loading');
                        button.removeClassName('pfg-refresh-success');
                        button.down('span').down('span').down('span').innerHTML = originalText;
                    }, 2000);
                } else {
                    throw new Error(result.message || 'Refresh failed');
                }
            } catch (e) {
                // Show error state
                button.removeClassName('pfg-refresh-loading');
                button.down('span').down('span').down('span').innerHTML = 'Error!';
                alert('Failed to refresh settings: ' + e.message);

                // Reset button after 2 seconds
                setTimeout(function() {
                    button.down('span').down('span').down('span').innerHTML = originalText;
                }, 2000);
            }
        },
        onFailure: function() {
            button.removeClassName('pfg-refresh-loading');
            button.down('span').down('span').down('span').innerHTML = 'Error!';
            alert('Failed to refresh settings. Please try again.');

            // Reset button after 2 seconds
            setTimeout(function() {
                button.down('span').down('span').down('span').innerHTML = originalText;
            }, 2000);
        }
    });
};

//]]>
</script>

<style type="text/css">
/* Shared CloudFlare Status Styles */
.pfg-cache-status {
    font-size: 11px;
    font-weight: bold;
}

.pfg-cache-status.loading {
    color: #f0ad4e;
}

.pfg-cache-status.success {
    color: #5cb85c;
}

.pfg-cache-status.error {
    color: #d9534f;
}

.pfg-cloudflare-status {
    background: #f8f8f8;
    border: 1px solid #ddd;
    padding: 15px;
    margin: 10px 0;
    border-radius: 3px;
}

.pfg-cloudflare-status .status-section {
    margin-bottom: 20px;
}

.pfg-cloudflare-status .status-section:last-child {
    margin-bottom: 0;
}

.pfg-cloudflare-status h4 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 13px;
    font-weight: bold;
}

.pfg-cloudflare-status .status-info {
    margin-bottom: 10px;
}

.pfg-cloudflare-status .status-indicator {
    font-weight: bold;
    padding: 5px 10px;
    border-radius: 3px;
    display: inline-block;
}

.pfg-cloudflare-status .success {
    color: #3d6611;
    background: #dff0d8;
    border: 1px solid #d6e9c6;
}

.pfg-cloudflare-status .error {
    color: #df280a;
    background: #f2dede;
    border: 1px solid #ebccd1;
}

.pfg-cloudflare-status .notice {
    color: #eb5e00;
    background: #fcf8e3;
    border: 1px solid #faebcc;
}

.pfg-cloudflare-status .zone-info-table {
    width: 100%;
    border-collapse: collapse;
}

.pfg-cloudflare-status .zone-info-table td {
    padding: 5px 10px 5px 0;
    vertical-align: top;
}

.pfg-cloudflare-status .zone-info-table .label {
    font-weight: bold;
    width: 120px;
    color: #666;
}

.pfg-cloudflare-status .zone-info-table .value {
    color: #333;
}

.pfg-cloudflare-status code {
    background: #f5f5f5;
    padding: 2px 4px;
    border: 1px solid #ddd;
    font-family: monospace;
    font-size: 11px;
}

.pfg-cloudflare-status .quick-actions button {
    margin-right: 10px;
}

.pfg-refresh-loading {
    opacity: 0.6;
    pointer-events: none;
}

.pfg-refresh-success {
    background-color: #d4edda !important;
    border-color: #c3e6cb !important;
    color: #155724 !important;
}
</style>



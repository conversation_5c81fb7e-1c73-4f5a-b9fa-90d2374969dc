<?php
/**
 * CloudFlare AJAX Settings Handler
 * This template provides AJAX functionality for all CloudFlare settings
 */
?>

<script type="text/javascript">
//<![CDATA[
document.observe('dom:loaded', function() {
    // CloudFlare settings that should sync via AJAX
    var cloudflareSettings = {
        'pfg_cloudflare_zone_settings_development_mode': 'development_mode',
        'pfg_cloudflare_zone_settings_security_level': 'security_level',
        'pfg_cloudflare_zone_settings_ssl': 'ssl',
        'pfg_cloudflare_zone_settings_cache_level': 'cache_level',
        'pfg_cloudflare_zone_settings_browser_cache_ttl': 'browser_cache_ttl',
        'pfg_cloudflare_optimization_minify_css': 'minify_css',
        'pfg_cloudflare_optimization_minify_html': 'minify_html',
        'pfg_cloudflare_optimization_minify_js': 'minify_js',
        'pfg_cloudflare_optimization_rocket_loader': 'rocket_loader',
        'pfg_cloudflare_optimization_polish': 'polish',
        'pfg_cloudflare_optimization_brotli': 'brotli'
    };
    
    var ajaxUrl = '<?php echo Mage::helper('adminhtml')->getUrl('pfg_cloudflare/adminhtml_system/updateZoneSetting') ?>';
    var formKey = '<?php echo Mage::getSingleton('core/session')->getFormKey() ?>';
    
    // Add event listeners to all CloudFlare settings
    Object.keys(cloudflareSettings).forEach(function(elementId) {
        var element = $(elementId);
        if (element) {
            var settingId = cloudflareSettings[elementId];
            
            // Load current value from CloudFlare on page load
            loadCurrentValue(elementId, settingId);
            
            // Add change event listener
            if (element.type === 'checkbox' || element.classList.contains('pfg-toggle-input')) {
                // Handle toggle switches and checkboxes
                element.observe('change', function() {
                    var value = this.checked ? 'on' : 'off';
                    updateCloudFlareSetting(elementId, settingId, value);
                });
            } else if (element.tagName === 'SELECT') {
                // Handle dropdowns
                element.observe('change', function() {
                    updateCloudFlareSetting(elementId, settingId, this.value);
                });
            } else {
                // Handle text inputs
                element.observe('change', function() {
                    updateCloudFlareSetting(elementId, settingId, this.value);
                });
            }
        }
    });
    
    /**
     * Load current value from CloudFlare
     */
    function loadCurrentValue(elementId, settingId) {
        new Ajax.Request(ajaxUrl, {
            method: 'post',
            parameters: {
                action: 'get',
                setting_id: settingId,
                form_key: formKey
            },
            onSuccess: function(response) {
                try {
                    var result = response.responseText.evalJSON();
                    if (result.success && result.data && result.data.value !== undefined) {
                        var element = $(elementId);
                        if (element) {
                            updateElementValue(element, settingId, result.data.value);
                        }
                    }
                } catch (e) {
                    console.log('Error parsing CloudFlare response for ' + settingId + ':', e);
                }
            },
            onFailure: function() {
                console.log('Failed to load current value for ' + settingId);
            }
        });
    }
    
    /**
     * Update CloudFlare setting via AJAX
     */
    function updateCloudFlareSetting(elementId, settingId, value) {
        var element = $(elementId);
        if (!element) return;
        
        // Show loading state
        showLoadingState(element);
        
        // Prepare value based on setting type
        var apiValue = prepareApiValue(settingId, value, element);
        
        new Ajax.Request(ajaxUrl, {
            method: 'post',
            parameters: {
                action: 'update',
                setting_id: settingId,
                value: Object.isString(apiValue) ? apiValue : Object.toJSON(apiValue),
                form_key: formKey
            },
            onSuccess: function(response) {
                try {
                    var result = response.responseText.evalJSON();
                    if (result.success) {
                        showSuccessState(element, result.message || 'Setting updated successfully');
                    } else {
                        showErrorState(element, result.message || 'Failed to update setting');
                    }
                } catch (e) {
                    showErrorState(element, 'Error processing response');
                }
            },
            onFailure: function() {
                showErrorState(element, 'Network error - please try again');
            }
        });
    }
    
    /**
     * Prepare API value based on setting type
     */
    function prepareApiValue(settingId, value, element) {
        switch (settingId) {
            case 'minify_css':
            case 'minify_html':
            case 'minify_js':
                // Handle individual minify toggles
                return value === '1' ? 'on' : 'off';
                
            case 'development_mode':
            case 'rocket_loader':
            case 'brotli':
                // Convert boolean/toggle values
                if (value === true || value === 'on' || value === '1') {
                    return 'on';
                } else {
                    return 'off';
                }
                
            case 'browser_cache_ttl':
                // Ensure numeric value
                return parseInt(value) || 14400;
                
            default:
                return value;
        }
    }
    
    /**
     * Update element value from CloudFlare response
     */
    function updateElementValue(element, settingId, value) {
        switch (settingId) {
            case 'minify_css':
            case 'minify_html':
            case 'minify_js':
                // Handle individual minify toggles
                element.value = value === 'on' ? '1' : '0';
                // Update toggle display if it exists
                var toggleElement = element.parentNode.querySelector('.pfg-toggle');
                if (toggleElement) {
                    toggleElement.className = 'pfg-toggle ' + (value === 'on' ? 'on' : 'off');
                }
                break;
                
            case 'development_mode':
            case 'rocket_loader':
            case 'brotli':
                // Handle toggle switches
                if (element.type === 'checkbox' || element.classList.contains('pfg-toggle-input')) {
                    element.checked = (value === 'on' || value === true);
                    // Trigger visual update for custom toggles
                    if (element.classList.contains('pfg-toggle-input')) {
                        var event = document.createEvent('HTMLEvents');
                        event.initEvent('change', true, false);
                        element.dispatchEvent(event);
                    }
                }
                break;
                
            default:
                // Handle regular selects and inputs
                if (element.tagName === 'SELECT') {
                    element.value = value;
                } else {
                    element.value = value;
                }
                break;
        }
    }
    
    /**
     * Show loading state
     */
    function showLoadingState(element) {
        element.style.backgroundColor = '#ffffcc';
        element.style.border = '1px solid #f0ad4e';
        if (element.nextSibling && element.nextSibling.className === 'cloudflare-status') {
            element.nextSibling.remove();
        }
    }
    
    /**
     * Show success state
     */
    function showSuccessState(element, message) {
        element.style.backgroundColor = '#dff0d8';
        element.style.border = '1px solid #5cb85c';
        
        var statusElement = document.createElement('span');
        statusElement.className = 'cloudflare-status success';
        statusElement.innerHTML = '✓ ' + message;
        statusElement.style.cssText = 'color: #5cb85c; font-size: 11px; margin-left: 5px;';
        
        if (element.nextSibling && element.nextSibling.className === 'cloudflare-status') {
            element.nextSibling.remove();
        }
        element.parentNode.insertBefore(statusElement, element.nextSibling);
        
        // Reset after 3 seconds
        setTimeout(function() {
            element.style.backgroundColor = '';
            element.style.border = '';
            if (statusElement.parentNode) {
                statusElement.remove();
            }
        }, 3000);
    }
    
    /**
     * Show error state
     */
    function showErrorState(element, message) {
        element.style.backgroundColor = '#f2dede';
        element.style.border = '1px solid #d9534f';
        
        var statusElement = document.createElement('span');
        statusElement.className = 'cloudflare-status error';
        statusElement.innerHTML = '✗ ' + message;
        statusElement.style.cssText = 'color: #d9534f; font-size: 11px; margin-left: 5px;';
        
        if (element.nextSibling && element.nextSibling.className === 'cloudflare-status') {
            element.nextSibling.remove();
        }
        element.parentNode.insertBefore(statusElement, element.nextSibling);
        
        // Reset after 5 seconds
        setTimeout(function() {
            element.style.backgroundColor = '';
            element.style.border = '';
            if (statusElement.parentNode) {
                statusElement.remove();
            }
        }, 5000);
    }
});
//]]>
</script>

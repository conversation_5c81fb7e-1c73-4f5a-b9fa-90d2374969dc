<button type="button" 
        id="<?php echo $this->getHtmlId() ?>_button" 
        class="scalable" 
        onclick="purgeUrls('<?php echo $this->getHtmlId() ?>')">
    <span>Purge URLs</span>
</button>

<span id="<?php echo $this->getHtmlId() ?>_status" class="pfg-cache-status" style="margin-left: 10px;"></span>

<script type="text/javascript">
//<![CDATA[
function purgeUrls(elementId) {
    var button = $(elementId + '_button');
    var status = $(elementId + '_status');

    // Debug: List all textarea elements
    var allTextareas = document.getElementsByTagName('textarea');
    console.log('All textarea elements found:');
    for (var i = 0; i < allTextareas.length; i++) {
        console.log('Textarea ' + i + ':', {
            id: allTextareas[i].id,
            name: allTextareas[i].name,
            value: allTextareas[i].value ? allTextareas[i].value.substring(0, 50) + '...' : 'empty'
        });
    }

    // Try to find the URLs textarea field by the expected ID first
    var urlsField = $('pfg_cloudflare_cache_management_purge_urls');
    console.log('Direct ID lookup result:', urlsField);

    // If not found, search for it by pattern
    if (!urlsField) {
        for (var i = 0; i < allTextareas.length; i++) {
            if (allTextareas[i].id && allTextareas[i].id.indexOf('purge_urls') !== -1 && allTextareas[i].id.indexOf('button') === -1) {
                urlsField = allTextareas[i];
                console.log('Found by pattern:', urlsField.id);
                break;
            }
        }
    }

    // If still not found, try to find any textarea in the cache management section
    if (!urlsField) {
        for (var i = 0; i < allTextareas.length; i++) {
            if (allTextareas[i].id && allTextareas[i].id.indexOf('cache_management') !== -1) {
                urlsField = allTextareas[i];
                console.log('Found by cache_management pattern:', urlsField.id);
                break;
            }
        }
    }

    if (!button || !status || !urlsField) {
        console.log('Missing elements:', {
            button: !!button,
            status: !!status,
            urlsField: !!urlsField,
            expectedId: 'pfg_cloudflare_cache_management_purge_urls'
        });
        if (status) {
            showCacheStatus(status, 'error', 'Could not find URLs field - check console');
        }
        return;
    }
    
    var urls = urlsField.value.trim();
    if (!urls) {
        showCacheStatus(status, 'error', 'Please enter URLs to purge (comma or line separated)');
        return;
    }
    
    // Validate URLs
    var urlArray = urls.split(/[,\n]/).map(function(url) {
        return url.trim();
    }).filter(function(url) {
        return url !== '';
    });
    
    if (urlArray.length === 0) {
        showCacheStatus(status, 'error', 'Please enter valid URLs');
        return;
    }
    
    if (urlArray.length > 30) {
        showCacheStatus(status, 'error', 'Maximum 30 URLs allowed per request');
        return;
    }
    
    // Show loading state
    button.disabled = true;
    button.innerHTML = '<span>Purging...</span>';
    status.innerHTML = '';
    status.className = 'pfg-cache-status loading';
    
    new Ajax.Request('<?php echo $this->getAjaxUrl() ?>', {
        method: 'post',
        parameters: {
            urls: urls,
            form_key: '<?php echo $this->getFormKey() ?>'
        },
        onSuccess: function(response) {
            try {
                var result = response.responseText.evalJSON();
                if (result.success) {
                    showCacheStatus(status, 'success', result.message || 'URLs purged successfully');
                    urlsField.value = ''; // Clear the field on success
                } else {
                    showCacheStatus(status, 'error', result.message || 'Failed to purge URLs');
                }
            } catch (e) {
                showCacheStatus(status, 'error', 'Error processing response');
            }
            
            // Reset button
            button.disabled = false;
            button.innerHTML = '<span>Purge URLs</span>';
        },
        onFailure: function() {
            showCacheStatus(status, 'error', 'Network error - please try again');
            
            // Reset button
            button.disabled = false;
            button.innerHTML = '<span>Purge URLs</span>';
        }
    });
}

function showCacheStatus(element, type, message) {
    element.className = 'pfg-cache-status ' + type;
    
    if (type === 'success') {
        element.innerHTML = '✓ ' + message;
        element.style.color = '#5cb85c';
    } else {
        element.innerHTML = '✗ ' + message;
        element.style.color = '#d9534f';
    }
    
    // Clear status after 5 seconds
    setTimeout(function() {
        element.innerHTML = '';
        element.className = 'pfg-cache-status';
    }, 5000);
}
//]]>
</script>

<style type="text/css">
.pfg-cache-status {
    font-size: 11px;
    font-weight: bold;
}

.pfg-cache-status.loading {
    color: #f0ad4e;
}

.pfg-cache-status.success {
    color: #5cb85c;
}

.pfg-cache-status.error {
    color: #d9534f;
}
</style>

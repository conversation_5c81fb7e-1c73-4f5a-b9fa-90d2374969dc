<div class="pfg-toggle-container">
    <input type="hidden" name="<?php echo $this->getHtmlName() ?>" value="0" />
    <input type="checkbox" 
           id="<?php echo $this->getHtmlId() ?>" 
           name="<?php echo $this->getHtmlName() ?>" 
           value="1" 
           class="pfg-toggle-input"
           <?php echo $this->getValue() ? 'checked="checked"' : '' ?>
           <?php echo $this->getDisabled() ? 'disabled="disabled"' : '' ?> />
    <label for="<?php echo $this->getHtmlId() ?>" class="pfg-toggle-label">
        <span class="pfg-toggle-switch">
            <span class="pfg-toggle-slider"></span>
        </span>
        <span class="pfg-toggle-text">
            <span class="pfg-toggle-on">ON</span>
            <span class="pfg-toggle-off">OFF</span>
        </span>
    </label>
</div>

<style type="text/css">
.pfg-toggle-container {
    display: inline-block;
    position: relative;
}

.pfg-toggle-input {
    display: none;
}

.pfg-toggle-label {
    display: inline-flex;
    align-items: center;
    cursor: pointer;
    user-select: none;
}

.pfg-toggle-switch {
    position: relative;
    width: 50px;
    height: 24px;
    background-color: #ccc;
    border-radius: 12px;
    transition: background-color 0.3s ease;
    margin-right: 8px;
}

.pfg-toggle-slider {
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background-color: white;
    border-radius: 50%;
    transition: transform 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.pfg-toggle-input:checked + .pfg-toggle-label .pfg-toggle-switch {
    background-color: #4CAF50;
}

.pfg-toggle-input:checked + .pfg-toggle-label .pfg-toggle-slider {
    transform: translateX(26px);
}

.pfg-toggle-input:disabled + .pfg-toggle-label {
    opacity: 0.6;
    cursor: not-allowed;
}

.pfg-toggle-text {
    font-size: 12px;
    font-weight: bold;
    color: #666;
}

.pfg-toggle-on,
.pfg-toggle-off {
    display: none;
}

.pfg-toggle-input:checked + .pfg-toggle-label .pfg-toggle-on {
    display: inline;
    color: #4CAF50;
}

.pfg-toggle-input:not(:checked) + .pfg-toggle-label .pfg-toggle-off {
    display: inline;
    color: #999;
}

/* Hover effects */
.pfg-toggle-label:hover .pfg-toggle-switch {
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
}

.pfg-toggle-input:checked + .pfg-toggle-label:hover .pfg-toggle-switch {
    background-color: #45a049;
}
</style>

<button type="button"
        id="<?php echo $this->getHtmlId() ?>_button"
        class="scalable"
        onclick="purgeEverythingWithStatus('<?php echo $this->getHtmlId() ?>')">
    <span>Purge Everything</span>
</button>

<span id="<?php echo $this->getHtmlId() ?>_status" class="pfg-cache-status" style="margin-left: 10px;"></span>

<script type="text/javascript">
//<![CDATA[
function purgeEverythingWithStatus(elementId) {
    var button = $(elementId + '_button');
    var status = $(elementId + '_status');

    PFGCloudflare.purgeAllCache({
        url: '<?php echo $this->getAjaxUrl() ?>',
        button: button,
        statusElement: status,
        confirmMessage: 'Are you sure you want to purge all cache from Cloudflare? This action cannot be undone.',
        successMessage: 'All cache purged successfully',
        errorMessage: 'Failed to purge cache'
    });
}
//]]>
</script>



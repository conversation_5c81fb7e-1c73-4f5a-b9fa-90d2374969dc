<?php
/**
 * @var $this PFG_Cloudflare_Block_Adminhtml_System_Config_Connectionstatus
 */
$connectionStatus = $this->getConnectionStatus();
$zoneInfo = $this->getZoneInfo();
?>

<div id="<?php echo $this->getHtmlId() ?>" class="pfg-cloudflare-status">
    <!-- Connection Status -->
    <div class="status-section">
        <h4><?php echo $this->__('CloudFlare Connection Status') ?></h4>
        <div class="status-info">
            <span class="status-indicator <?php echo $connectionStatus['class'] ?>">
                <?php if ($connectionStatus['status'] === 'connected'): ?>
                    <?php echo $this->__('✓ Your store is connected to CloudFlare') ?>
                <?php elseif ($connectionStatus['status'] === 'not_configured'): ?>
                    <?php echo $this->__('⚠ Your store is not connected to CloudFlare - Please configure API credentials above') ?>
                <?php else: ?>
                    <?php echo $this->__('✗ Connection failed: ') . $this->escapeHtml($connectionStatus['message']) ?>
                <?php endif; ?>
            </span>
        </div>
    </div>

    <?php if ($this->isApiConfigured() && $zoneInfo): ?>
    <!-- Zone Information -->
    <div class="status-section">
        <h4><?php echo $this->__('Zone Information') ?></h4>
        <table class="zone-info-table">
            <tr>
                <td class="label"><?php echo $this->__('Zone Name:') ?></td>
                <td class="value"><?php echo $this->escapeHtml($zoneInfo['name']) ?></td>
            </tr>
            <tr>
                <td class="label"><?php echo $this->__('Zone ID:') ?></td>
                <td class="value"><code><?php echo $this->escapeHtml($zoneInfo['id']) ?></code></td>
            </tr>
            <tr>
                <td class="label"><?php echo $this->__('Status:') ?></td>
                <td class="value">
                    <span class="<?php echo $zoneInfo['status'] === 'active' ? 'success' : 'notice' ?>">
                        <?php echo $this->escapeHtml(ucfirst($zoneInfo['status'])) ?>
                    </span>
                </td>
            </tr>
            <?php if (isset($zoneInfo['plan']['name'])): ?>
            <tr>
                <td class="label"><?php echo $this->__('Plan:') ?></td>
                <td class="value"><?php echo $this->escapeHtml(ucfirst($zoneInfo['plan']['name'])) ?></td>
            </tr>
            <?php endif; ?>
            <?php if (isset($zoneInfo['created_on'])): ?>
            <tr>
                <td class="label"><?php echo $this->__('Created:') ?></td>
                <td class="value"><?php echo $this->formatDate($zoneInfo['created_on']) ?></td>
            </tr>
            <?php endif; ?>
            <?php if (isset($zoneInfo['modified_on'])): ?>
            <tr>
                <td class="label"><?php echo $this->__('Last Modified:') ?></td>
                <td class="value"><?php echo $this->formatDate($zoneInfo['modified_on']) ?></td>
            </tr>
            <?php endif; ?>
        </table>
    </div>
    <?php endif; ?>

    <!-- Quick Actions -->
    <div class="status-section">
        <h4><?php echo $this->__('Quick Actions') ?></h4>
        <div class="quick-actions">
            <?php if ($this->isApiConfigured()): ?>
            <button type="button" class="scalable" onclick="purgeAllCacheFromConfig()">
                <span><span><span><?php echo $this->__('Purge All Cache') ?></span></span></span>
            </button>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php if ($this->isApiConfigured()): ?>
<script type="text/javascript">
//<![CDATA[
function purgeAllCacheFromConfig() {
    if (!confirm('<?php echo $this->__('Are you sure you want to purge all cache from Cloudflare? This action cannot be undone.') ?>')) {
        return;
    }
    
    new Ajax.Request('<?php echo Mage::helper('adminhtml')->getUrl('pfg_cloudflare/adminhtml_system/purgeAllCache') ?>', {
        method: 'post',
        parameters: {
            form_key: FORM_KEY
        },
        onSuccess: function(response) {
            try {
                var result = response.responseText.evalJSON();
                if (result.success) {
                    alert('<?php echo $this->__('Success: ') ?>' + result.message);
                } else {
                    alert('<?php echo $this->__('Error: ') ?>' + result.message);
                }
            } catch (e) {
                alert('<?php echo $this->__('Error: Invalid response from server.') ?>');
            }
        },
        onFailure: function() {
            alert('<?php echo $this->__('Error: Cache purge failed. Please try again.') ?>');
        }
    });
}
//]]>
</script>
<?php endif; ?>

<style type="text/css">
.pfg-cloudflare-status {
    background: #f8f8f8;
    border: 1px solid #ddd;
    padding: 15px;
    margin: 10px 0;
    border-radius: 3px;
}

.pfg-cloudflare-status .status-section {
    margin-bottom: 20px;
}

.pfg-cloudflare-status .status-section:last-child {
    margin-bottom: 0;
}

.pfg-cloudflare-status h4 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 13px;
    font-weight: bold;
}

.pfg-cloudflare-status .status-info {
    margin-bottom: 10px;
}

.pfg-cloudflare-status .status-indicator {
    font-weight: bold;
    padding: 5px 10px;
    border-radius: 3px;
    display: inline-block;
}

.pfg-cloudflare-status .success { 
    color: #3d6611; 
    background: #dff0d8;
    border: 1px solid #d6e9c6;
}

.pfg-cloudflare-status .error { 
    color: #df280a; 
    background: #f2dede;
    border: 1px solid #ebccd1;
}

.pfg-cloudflare-status .notice { 
    color: #eb5e00; 
    background: #fcf8e3;
    border: 1px solid #faebcc;
}

.pfg-cloudflare-status .zone-info-table {
    width: 100%;
    border-collapse: collapse;
}

.pfg-cloudflare-status .zone-info-table td {
    padding: 5px 10px 5px 0;
    vertical-align: top;
}

.pfg-cloudflare-status .zone-info-table .label {
    font-weight: bold;
    width: 120px;
    color: #666;
}

.pfg-cloudflare-status .zone-info-table .value {
    color: #333;
}

.pfg-cloudflare-status code { 
    background: #f5f5f5; 
    padding: 2px 4px; 
    border: 1px solid #ddd; 
    font-family: monospace; 
    font-size: 11px;
}

.pfg-cloudflare-status .quick-actions button {
    margin-right: 10px;
}
</style>

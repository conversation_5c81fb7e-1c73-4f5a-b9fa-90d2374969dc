<?php
/**
 * @var $this PFG_Cloudflare_Block_Adminhtml_System_Config_Connectionstatus
 */
$connectionStatus = $this->getConnectionStatus();
$zoneInfo = $this->getZoneInfo();
?>

<div id="<?php echo $this->getHtmlId() ?>" class="pfg-cloudflare-status">
    <!-- Connection Status -->
    <div class="status-section">
        <h4><?php echo $this->__('CloudFlare Connection Status') ?></h4>
        <div class="status-info">
            <span class="status-indicator <?php echo $connectionStatus['class'] ?>">
                <?php if ($connectionStatus['status'] === 'connected'): ?>
                    <?php echo $this->__('✓ Your store is connected to CloudFlare') ?>
                <?php elseif ($connectionStatus['status'] === 'not_configured'): ?>
                    <?php echo $this->__('⚠ Your store is not connected to CloudFlare - Please configure API credentials above') ?>
                <?php else: ?>
                    <?php echo $this->__('✗ Connection failed: ') . $this->escapeHtml($connectionStatus['message']) ?>
                <?php endif; ?>
            </span>
        </div>
    </div>

    <?php if ($this->isApiConfigured() && $zoneInfo): ?>
    <!-- Zone Information -->
    <div class="status-section">
        <h4><?php echo $this->__('Zone Information') ?></h4>
        <table class="zone-info-table">
            <tr>
                <td class="label"><?php echo $this->__('Zone Name:') ?></td>
                <td class="value"><?php echo $this->escapeHtml($zoneInfo['name']) ?></td>
            </tr>
            <tr>
                <td class="label"><?php echo $this->__('Zone ID:') ?></td>
                <td class="value"><code><?php echo $this->escapeHtml($zoneInfo['id']) ?></code></td>
            </tr>
            <tr>
                <td class="label"><?php echo $this->__('Status:') ?></td>
                <td class="value">
                    <span class="<?php echo $zoneInfo['status'] === 'active' ? 'success' : 'notice' ?>">
                        <?php echo $this->escapeHtml(ucfirst($zoneInfo['status'])) ?>
                    </span>
                </td>
            </tr>
            <?php if (isset($zoneInfo['plan']['name'])): ?>
            <tr>
                <td class="label"><?php echo $this->__('Plan:') ?></td>
                <td class="value"><?php echo $this->escapeHtml(ucfirst($zoneInfo['plan']['name'])) ?></td>
            </tr>
            <?php endif; ?>
            <?php if (isset($zoneInfo['created_on'])): ?>
            <tr>
                <td class="label"><?php echo $this->__('Created:') ?></td>
                <td class="value"><?php echo $this->formatDate($zoneInfo['created_on']) ?></td>
            </tr>
            <?php endif; ?>
            <?php if (isset($zoneInfo['modified_on'])): ?>
            <tr>
                <td class="label"><?php echo $this->__('Last Modified:') ?></td>
                <td class="value"><?php echo $this->formatDate($zoneInfo['modified_on']) ?></td>
            </tr>
            <?php endif; ?>
        </table>
    </div>
    <?php endif; ?>

    <!-- Quick Actions -->
    <div class="status-section">
        <h4><?php echo $this->__('Quick Actions') ?></h4>
        <div class="quick-actions">
            <?php if ($this->isApiConfigured()): ?>
            <button type="button" class="scalable" onclick="PFGCloudflare.purgeAllCache()">
                <span><span><span><?php echo $this->__('Purge All Cache') ?></span></span></span>
            </button>
            <?php endif; ?>
        </div>
    </div>
</div>



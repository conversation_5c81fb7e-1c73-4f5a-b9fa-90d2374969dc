<?php
/**
 * @var $this PFG_Cloudflare_Block_Adminhtml_Dashboard
 */
$connectionStatus = $this->getConnectionStatus();
$zoneInfo = $this->getZoneInfo();
?>

<div class="content-header">
    <h3 class="icon-head head-adminhtml-cloudflare"><?php echo $this->__('Cloudflare Integration Dashboard') ?></h3>
</div>

<div class="entry-edit">
    <!-- Connection Status -->
    <div class="entry-edit-head">
        <h4 class="icon-head head-edit-form fieldset-legend"><?php echo $this->__('Connection Status') ?></h4>
    </div>
    <div class="fieldset">
        <div class="hor-scroll">
            <table cellspacing="0" class="form-list">
                <tbody>
                    <tr>
                        <td class="label"><label><?php echo $this->__('Status') ?></label></td>
                        <td class="value">
                            <span class="<?php echo $connectionStatus['class'] ?>">
                                <?php echo $this->escapeHtml($connectionStatus['message']) ?>
                            </span>
                        </td>
                    </tr>
                    <?php if (!$this->isApiConfigured()): ?>
                    <tr>
                        <td class="label"></td>
                        <td class="value">
                            <button type="button" class="scalable" onclick="setLocation('<?php echo $this->getConfigUrl() ?>')">
                                <span><span><span><?php echo $this->__('Configure API Settings') ?></span></span></span>
                            </button>
                        </td>
                    </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<?php if ($this->isApiConfigured() && $zoneInfo): ?>
<div class="entry-edit">
    <!-- Zone Information -->
    <div class="entry-edit-head">
        <h4 class="icon-head head-edit-form fieldset-legend"><?php echo $this->__('Zone Information') ?></h4>
    </div>
    <div class="fieldset">
        <div class="hor-scroll">
            <table cellspacing="0" class="form-list">
                <tbody>
                    <tr>
                        <td class="label"><label><?php echo $this->__('Zone Name') ?></label></td>
                        <td class="value"><?php echo $this->escapeHtml($zoneInfo['name']) ?></td>
                    </tr>
                    <tr>
                        <td class="label"><label><?php echo $this->__('Zone ID') ?></label></td>
                        <td class="value"><code><?php echo $this->escapeHtml($zoneInfo['id']) ?></code></td>
                    </tr>
                    <tr>
                        <td class="label"><label><?php echo $this->__('Status') ?></label></td>
                        <td class="value">
                            <span class="<?php echo $zoneInfo['status'] === 'active' ? 'success' : 'notice' ?>">
                                <?php echo $this->escapeHtml(ucfirst($zoneInfo['status'])) ?>
                            </span>
                        </td>
                    </tr>
                    <?php if (isset($zoneInfo['plan']['name'])): ?>
                    <tr>
                        <td class="label"><label><?php echo $this->__('Plan') ?></label></td>
                        <td class="value"><?php echo $this->escapeHtml(ucfirst($zoneInfo['plan']['name'])) ?></td>
                    </tr>
                    <?php endif; ?>
                    <?php if (isset($zoneInfo['created_on'])): ?>
                    <tr>
                        <td class="label"><label><?php echo $this->__('Created') ?></label></td>
                        <td class="value"><?php echo $this->formatDate($zoneInfo['created_on']) ?></td>
                    </tr>
                    <?php endif; ?>
                    <?php if (isset($zoneInfo['modified_on'])): ?>
                    <tr>
                        <td class="label"><label><?php echo $this->__('Last Modified') ?></label></td>
                        <td class="value"><?php echo $this->formatDate($zoneInfo['modified_on']) ?></td>
                    </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>
<?php endif; ?>

<div class="entry-edit">
    <!-- Quick Actions -->
    <div class="entry-edit-head">
        <h4 class="icon-head head-edit-form fieldset-legend"><?php echo $this->__('Quick Actions') ?></h4>
    </div>
    <div class="fieldset">
        <div class="hor-scroll">
            <table cellspacing="0" class="form-list">
                <tbody>
                    <tr>
                        <td class="label"><label><?php echo $this->__('Configuration') ?></label></td>
                        <td class="value">
                            <button type="button" class="scalable" onclick="setLocation('<?php echo $this->getConfigUrl() ?>')">
                                <span><span><span><?php echo $this->__('Manage Settings') ?></span></span></span>
                            </button>
                        </td>
                    </tr>
                    <?php if ($this->isApiConfigured()): ?>
                    <tr>
                        <td class="label"><label><?php echo $this->__('Cache Management') ?></label></td>
                        <td class="value">
                            <button type="button" class="scalable" onclick="PFGCloudflare.purgeAllCache()">
                                <span><span><span><?php echo $this->__('Purge All Cache') ?></span></span></span>
                            </button>
                        </td>
                    </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<style type="text/css">
.success { color: #3d6611; font-weight: bold; }
.error { color: #df280a; font-weight: bold; }
.notice { color: #eb5e00; font-weight: bold; }
code { background: #f5f5f5; padding: 2px 4px; border: 1px solid #ddd; font-family: monospace; }
</style>

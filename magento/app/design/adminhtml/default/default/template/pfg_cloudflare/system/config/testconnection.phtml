<button id="<?php echo $this->getHtmlId() ?>" title="<?php echo $this->__('Test Connection') ?>" type="button" class="scalable" onclick="testCloudflareConnection(); return false;">
    <span><span><span><?php echo $this->getButtonLabel() ?></span></span></span>
</button>

<script type="text/javascript">
//<![CDATA[
function testCloudflareConnection() {
    var button = $('<?php echo $this->getHtmlId() ?>');
    var originalText = button.innerHTML;
    
    // Disable button and show loading state
    button.disabled = true;
    button.innerHTML = '<span><span><span><?php echo $this->__('Testing...') ?></span></span></span>';
    
    // Get form values - try multiple possible field IDs
    var accountId = '';
    var zoneId = '';
    var globalApiKey = '';
    var email = '';

    // Try different possible field ID formats
    var possibleIds = [
        'pfg_cloudflare_general_account_id',
        'pfg_cloudflare_account_id',
        'account_id'
    ];

    for (var i = 0; i < possibleIds.length; i++) {
        if ($(possibleIds[i]) && $(possibleIds[i]).value) {
            accountId = $(possibleIds[i]).value;
            break;
        }
    }

    possibleIds = [
        'pfg_cloudflare_general_zone_id',
        'pfg_cloudflare_zone_id',
        'zone_id'
    ];

    for (var i = 0; i < possibleIds.length; i++) {
        if ($(possibleIds[i]) && $(possibleIds[i]).value) {
            zoneId = $(possibleIds[i]).value;
            break;
        }
    }

    possibleIds = [
        'pfg_cloudflare_general_global_api_key',
        'pfg_cloudflare_global_api_key',
        'global_api_key'
    ];

    for (var i = 0; i < possibleIds.length; i++) {
        if ($(possibleIds[i]) && $(possibleIds[i]).value) {
            globalApiKey = $(possibleIds[i]).value;
            break;
        }
    }

    possibleIds = [
        'pfg_cloudflare_general_email',
        'pfg_cloudflare_email',
        'email'
    ];

    for (var i = 0; i < possibleIds.length; i++) {
        if ($(possibleIds[i]) && $(possibleIds[i]).value) {
            email = $(possibleIds[i]).value;
            break;
        }
    }

    // Validate required fields
    if (!accountId || !zoneId || !globalApiKey || !email) {
        alert('<?php echo $this->__('Please fill in all required fields before testing the connection.') ?>');
        button.disabled = false;
        button.innerHTML = originalText;
        return;
    }
    
    // Make AJAX request
    new Ajax.Request('<?php echo $this->getAjaxUrl() ?>', {
        method: 'post',
        parameters: {
            account_id: accountId,
            zone_id: zoneId,
            global_api_key: globalApiKey,
            email: email,
            form_key: FORM_KEY
        },
        onSuccess: function(response) {
            button.disabled = false;
            button.innerHTML = originalText;
            
            try {
                var result = response.responseText.evalJSON();
                if (result.success) {
                    alert('<?php echo $this->__('Success: Your store is now connected to CloudFlare! ') ?>' + result.message + '\n\n<?php echo $this->__('Please save the configuration and refresh the page to see your CloudFlare zone information.') ?>');
                    // Refresh the page after successful connection to show updated status
                    setTimeout(function() {
                        window.location.reload();
                    }, 2000);
                } else {
                    alert('<?php echo $this->__('Connection Failed: ') ?>' + result.message);
                }
            } catch (e) {
                alert('<?php echo $this->__('Error: Invalid response from server.') ?>');
            }
        },
        onFailure: function() {
            button.disabled = false;
            button.innerHTML = originalText;
            alert('<?php echo $this->__('Error: Connection test failed. Please try again.') ?>');
        }
    });
}
//]]>
</script>

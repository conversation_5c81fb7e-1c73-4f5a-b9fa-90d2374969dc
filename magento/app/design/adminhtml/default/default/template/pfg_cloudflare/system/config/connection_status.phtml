<?php
$connectionStatus = $this->getConnectionStatus();
$zoneInfo = $this->getZoneInfo();
?>

<div class="pfg-connection-status">
    <span class="status-indicator <?php echo $connectionStatus['class'] ?>">
        <?php echo $connectionStatus['message'] ?>
    </span>
    
    <?php if ($connectionStatus['status'] === 'connected' && $zoneInfo): ?>
        <div class="zone-info" style="margin-top: 5px; font-size: 11px; color: #666;">
            <strong>Zone:</strong> <?php echo $this->escapeHtml($zoneInfo['name']) ?><br/>
            <strong>Status:</strong> <?php echo ucfirst($zoneInfo['status']) ?><br/>
            <?php if (isset($zoneInfo['created_on'])): ?>
                <strong>Created:</strong> <?php echo $this->formatDate($zoneInfo['created_on']) ?>
            <?php endif; ?>
        </div>
    <?php endif; ?>
</div>

<style type="text/css">
.pfg-connection-status .status-indicator {
    font-weight: bold;
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 11px;
}

.pfg-connection-status .status-indicator.success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.pfg-connection-status .status-indicator.error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.pfg-connection-status .zone-info {
    background-color: #f8f9fa;
    padding: 5px 8px;
    border-radius: 3px;
    border: 1px solid #dee2e6;
}
</style>

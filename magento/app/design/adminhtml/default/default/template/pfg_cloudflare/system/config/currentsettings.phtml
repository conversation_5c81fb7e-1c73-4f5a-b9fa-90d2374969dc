<?php
/**
 * @var $this PFG_Cloudflare_Block_Adminhtml_System_Config_Currentsettings
 */
$currentSettings = $this->getCurrentSettings();
?>

<div id="<?php echo $this->getHtmlId() ?>" class="pfg-cloudflare-current-settings">
    <?php if (!$this->isApiConfigured()): ?>
        <div class="settings-notice">
            <span class="notice-icon">⚠</span>
            <span class="notice-text"><?php echo $this->__('Please configure and connect to CloudFlare to view current settings.') ?></span>
        </div>
    <?php elseif ($currentSettings === null): ?>
        <div class="settings-notice error">
            <span class="notice-icon">✗</span>
            <span class="notice-text"><?php echo $this->__('Unable to fetch current CloudFlare settings. Please check your connection.') ?></span>
        </div>
    <?php elseif (empty($currentSettings)): ?>
        <div class="settings-notice">
            <span class="notice-icon">ℹ</span>
            <span class="notice-text"><?php echo $this->__('No settings found. This may be normal for new zones.') ?></span>
        </div>
    <?php else: ?>
        <div class="settings-header">
            <h4><?php echo $this->__('Current CloudFlare Settings') ?></h4>
            <p class="settings-description"><?php echo $this->__('These are the current settings active on your CloudFlare zone:') ?></p>
        </div>
        
        <div class="settings-grid">
            <div class="settings-column">
                <h5><?php echo $this->__('Zone Settings') ?></h5>
                <table class="settings-table">
                    <?php if (isset($currentSettings['security_level'])): ?>
                    <tr>
                        <td class="setting-label"><?php echo $this->getSettingLabel('security_level') ?>:</td>
                        <td class="setting-value"><?php echo $this->escapeHtml($this->formatSettingValue('security_level', $currentSettings['security_level'])) ?></td>
                    </tr>
                    <?php endif; ?>
                    
                    <?php if (isset($currentSettings['ssl'])): ?>
                    <tr>
                        <td class="setting-label"><?php echo $this->getSettingLabel('ssl') ?>:</td>
                        <td class="setting-value"><?php echo $this->escapeHtml($this->formatSettingValue('ssl', $currentSettings['ssl'])) ?></td>
                    </tr>
                    <?php endif; ?>
                    
                    <?php if (isset($currentSettings['cache_level'])): ?>
                    <tr>
                        <td class="setting-label"><?php echo $this->getSettingLabel('cache_level') ?>:</td>
                        <td class="setting-value"><?php echo $this->escapeHtml($this->formatSettingValue('cache_level', $currentSettings['cache_level'])) ?></td>
                    </tr>
                    <?php endif; ?>
                    
                    <?php if (isset($currentSettings['browser_cache_ttl'])): ?>
                    <tr>
                        <td class="setting-label"><?php echo $this->getSettingLabel('browser_cache_ttl') ?>:</td>
                        <td class="setting-value"><?php echo $this->escapeHtml($this->formatSettingValue('browser_cache_ttl', $currentSettings['browser_cache_ttl'])) ?></td>
                    </tr>
                    <?php endif; ?>
                    
                    <?php if (isset($currentSettings['development_mode'])): ?>
                    <tr>
                        <td class="setting-label"><?php echo $this->getSettingLabel('development_mode') ?>:</td>
                        <td class="setting-value">
                            <span class="status-badge <?php echo ($currentSettings['development_mode'] === 'on' || $currentSettings['development_mode'] === true) ? 'status-on' : 'status-off' ?>">
                                <?php echo $this->formatSettingValue('development_mode', $currentSettings['development_mode']) ?>
                            </span>
                        </td>
                    </tr>
                    <?php endif; ?>
                </table>
            </div>
            
            <div class="settings-column">
                <h5><?php echo $this->__('Optimization Settings') ?></h5>
                <table class="settings-table">
                    <?php if (isset($currentSettings['minify'])): ?>
                    <tr>
                        <td class="setting-label"><?php echo $this->getSettingLabel('minify') ?>:</td>
                        <td class="setting-value"><?php echo $this->escapeHtml($this->formatSettingValue('minify', $currentSettings['minify'])) ?></td>
                    </tr>
                    <?php endif; ?>
                    
                    <?php if (isset($currentSettings['rocket_loader'])): ?>
                    <tr>
                        <td class="setting-label"><?php echo $this->getSettingLabel('rocket_loader') ?>:</td>
                        <td class="setting-value">
                            <span class="status-badge <?php echo ($currentSettings['rocket_loader'] === 'on' || $currentSettings['rocket_loader'] === true) ? 'status-on' : 'status-off' ?>">
                                <?php echo $this->formatSettingValue('rocket_loader', $currentSettings['rocket_loader']) ?>
                            </span>
                        </td>
                    </tr>
                    <?php endif; ?>
                    
                    <?php if (isset($currentSettings['polish'])): ?>
                    <tr>
                        <td class="setting-label"><?php echo $this->getSettingLabel('polish') ?>:</td>
                        <td class="setting-value"><?php echo $this->escapeHtml($this->formatSettingValue('polish', $currentSettings['polish'])) ?></td>
                    </tr>
                    <?php endif; ?>
                    
                    <?php if (isset($currentSettings['brotli'])): ?>
                    <tr>
                        <td class="setting-label"><?php echo $this->getSettingLabel('brotli') ?>:</td>
                        <td class="setting-value">
                            <span class="status-badge <?php echo ($currentSettings['brotli'] === 'on' || $currentSettings['brotli'] === true) ? 'status-on' : 'status-off' ?>">
                                <?php echo $this->formatSettingValue('brotli', $currentSettings['brotli']) ?>
                            </span>
                        </td>
                    </tr>
                    <?php endif; ?>
                </table>
            </div>
        </div>
        
        <div class="settings-footer">
            <p class="settings-note">
                <?php echo $this->__('Note: Changes made in the configuration above will be applied to CloudFlare when you save the configuration.') ?>
            </p>
        </div>
    <?php endif; ?>
</div>

<style type="text/css">
.pfg-cloudflare-current-settings {
    background: #f8f8f8;
    border: 1px solid #ddd;
    padding: 15px;
    margin: 10px 0;
    border-radius: 3px;
}

.settings-notice {
    display: flex;
    align-items: center;
    padding: 10px;
    border-radius: 3px;
    background: #fcf8e3;
    border: 1px solid #faebcc;
    color: #eb5e00;
}

.settings-notice.error {
    background: #f2dede;
    border: 1px solid #ebccd1;
    color: #df280a;
}

.notice-icon {
    font-size: 16px;
    margin-right: 8px;
}

.notice-text {
    font-weight: 500;
}

.settings-header {
    margin-bottom: 20px;
}

.settings-header h4 {
    margin: 0 0 5px 0;
    color: #333;
    font-size: 14px;
    font-weight: bold;
}

.settings-description {
    margin: 0;
    color: #666;
    font-size: 12px;
}

.settings-grid {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;
}

.settings-column {
    flex: 1;
}

.settings-column h5 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 13px;
    font-weight: bold;
    border-bottom: 1px solid #ddd;
    padding-bottom: 5px;
}

.settings-table {
    width: 100%;
    border-collapse: collapse;
}

.settings-table td {
    padding: 6px 8px;
    vertical-align: top;
    border-bottom: 1px solid #eee;
}

.setting-label {
    font-weight: 500;
    color: #666;
    width: 40%;
}

.setting-value {
    color: #333;
    font-weight: normal;
}

.status-badge {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
}

.status-on {
    background: #dff0d8;
    color: #3d6611;
    border: 1px solid #d6e9c6;
}

.status-off {
    background: #f2dede;
    color: #df280a;
    border: 1px solid #ebccd1;
}

.settings-footer {
    border-top: 1px solid #ddd;
    padding-top: 10px;
    margin-top: 15px;
}

.settings-note {
    margin: 0;
    color: #666;
    font-size: 11px;
    font-style: italic;
}

@media (max-width: 768px) {
    .settings-grid {
        flex-direction: column;
        gap: 15px;
    }
}
</style>

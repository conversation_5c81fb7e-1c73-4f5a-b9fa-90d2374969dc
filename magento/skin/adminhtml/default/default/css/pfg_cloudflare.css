/**
 * PFG CloudFlare Integration - Admin Styles
 * Responsive two-column layouts for configuration interface
 */

/* General Settings Two-Column Layout */
.pfg-two-column-container {
    display: flex;
    gap: 20px;
    width: 100%;
    margin-top: 20px;
}

.pfg-column-left,
.pfg-column-right {
    flex: 1;
    min-width: 0;
}

.pfg-connection-status-container {
    background: #f8f8f8;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
}

.pfg-connection-status-container h4 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 13px;
    font-weight: bold;
}

/* Zone Settings Two-Column Layout */
.pfg-zone-two-column-container {
    display: flex;
    gap: 30px;
    width: 100%;
    margin: 20px 0;
    padding: 15px;
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.pfg-zone-column-left,
.pfg-zone-column-right {
    flex: 1;
    min-width: 0;
}

.pfg-zone-column-left h4,
.pfg-zone-column-right h4 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 14px;
    font-weight: bold;
    border-bottom: 2px solid #0073aa;
    padding-bottom: 5px;
}

.pfg-zone-column-right h4 {
    border-bottom-color: #00a32a;
}

.pfg-zone-settings-info,
.pfg-optimization-settings-info {
    font-size: 12px;
    color: #666;
    line-height: 1.4;
}

.pfg-zone-settings-info p,
.pfg-optimization-settings-info p {
    margin: 0;
}

/* Visual grouping for zone settings fields */
tr[id*="development_mode"] td.label,
tr[id*="security_level"] td.label,
tr[id*="ssl"] td.label,
tr[id*="cache_level"] td.label,
tr[id*="browser_cache_ttl"] td.label {
    border-left: 3px solid #0073aa;
    padding-left: 12px;
    background-color: #f8f9fa;
}

/* Visual grouping for optimization fields */
tr[id*="minify"] td.label,
tr[id*="rocket_loader"] td.label,
tr[id*="polish"] td.label,
tr[id*="brotli"] td.label {
    border-left: 3px solid #00a32a;
    padding-left: 12px;
    background-color: #f0f8f0;
}

/* Hide empty layout rows */
#pfg-cloudflare-two-column-layout .label,
#pfg-cloudflare-two-column-layout .value,
#pfg-cloudflare-zone-two-column-layout .label,
#pfg-cloudflare-zone-two-column-layout .value {
    padding: 0;
    border: none;
}

#pfg-cloudflare-two-column-layout,
#pfg-cloudflare-zone-two-column-layout {
    background: none;
}

/* Responsive behavior for tablets and smaller screens */
@media (max-width: 768px) {
    .pfg-two-column-container,
    .pfg-zone-two-column-container {
        flex-direction: column;
        gap: 20px;
    }
    
    .pfg-zone-two-column-container {
        gap: 15px;
    }
}

/* Responsive behavior for mobile screens */
@media (max-width: 480px) {
    .pfg-two-column-container,
    .pfg-zone-two-column-container {
        margin: 10px 0;
        padding: 10px;
    }
    
    .pfg-connection-status-container {
        padding: 10px;
    }
    
    .pfg-zone-column-left h4,
    .pfg-zone-column-right h4 {
        font-size: 13px;
    }
}

/* Enhanced visual feedback for configuration sections */
.pfg-cloudflare-section-header {
    background: linear-gradient(135deg, #0073aa 0%, #005177 100%);
    color: white;
    padding: 10px 15px;
    margin: -10px -15px 15px -15px;
    border-radius: 4px 4px 0 0;
    font-weight: bold;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.pfg-optimization-section-header {
    background: linear-gradient(135deg, #00a32a 0%, #007a1f 100%);
    color: white;
    padding: 10px 15px;
    margin: -10px -15px 15px -15px;
    border-radius: 4px 4px 0 0;
    font-weight: bold;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

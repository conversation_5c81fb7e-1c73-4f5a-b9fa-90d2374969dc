<?php
/**
 * PFG Cloudflare Integration Module
 * 
 * @category   PFG
 * @package    PFG_Cloudflare
 * <AUTHOR> Development Team
 */

class PFG_Cloudflare_Adminhtml_DashboardController extends Mage_Adminhtml_Controller_Action
{
    /**
     * Dashboard index action
     */
    public function indexAction()
    {
        $this->_title($this->__('PFG'))->_title($this->__('Cloudflare Integration'));
        
        $this->loadLayout();
        $this->_setActiveMenu('pfg/cloudflare');
        $this->_addBreadcrumb($this->__('PFG'), $this->__('PFG'));
        $this->_addBreadcrumb($this->__('Cloudflare Integration'), $this->__('Cloudflare Integration'));
        
        $this->renderLayout();
    }
    
    /**
     * Check ACL permissions
     *
     * @return bool
     */
    protected function _isAllowed()
    {
        return Mage::getSingleton('admin/session')->isAllowed('pfg/cloudflare');
    }
}

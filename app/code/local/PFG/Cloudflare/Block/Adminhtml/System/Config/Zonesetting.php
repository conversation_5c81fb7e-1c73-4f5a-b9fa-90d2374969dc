<?php
/**
 * PFG Cloudflare Integration Module
 * 
 * @category   PFG
 * @package    PFG_Cloudflare
 * <AUTHOR> Development Team
 */

class PFG_Cloudflare_Block_Adminhtml_System_Config_Zonesetting extends Mage_Adminhtml_Block_System_Config_Form_Field
{
    /**
     * Get the button and scripts contents
     *
     * @param Varien_Data_Form_Element_Abstract $element
     * @return string
     */
    protected function _getElementHtml(Varien_Data_Form_Element_Abstract $element)
    {
        $html = parent::_getElementHtml($element);
        
        // Add real-time update functionality
        $settingId = $this->_getSettingIdFromElement($element);
        $elementId = $element->getHtmlId();
        
        $html .= $this->_getZoneSettingScript($elementId, $settingId);
        
        return $html;
    }
    
    /**
     * Get setting ID from element
     *
     * @param Varien_Data_Form_Element_Abstract $element
     * @return string
     */
    protected function _getSettingIdFromElement($element)
    {
        $fieldName = $element->getName();
        
        // Extract setting name from field name
        if (preg_match('/\[([^]]+)\]$/', $fieldName, $matches)) {
            return $matches[1];
        }
        
        return '';
    }
    
    /**
     * Get zone setting JavaScript
     *
     * @param string $elementId
     * @param string $settingId
     * @return string
     */
    protected function _getZoneSettingScript($elementId, $settingId)
    {
        if (empty($settingId)) {
            return '';
        }
        
        $ajaxUrl = Mage::helper('adminhtml')->getUrl('pfg_cloudflare/adminhtml_system/updateZoneSetting');
        
        return "
        <script type=\"text/javascript\">
        //<![CDATA[
        document.observe('dom:loaded', function() {
            var element = $('{$elementId}');
            if (element) {
                // Load current value from Cloudflare
                loadZoneSettingValue('{$elementId}', '{$settingId}');
                
                // Add change event listener
                element.observe('change', function() {
                    updateZoneSetting('{$elementId}', '{$settingId}', this.value);
                });
            }
        });
        
        function loadZoneSettingValue(elementId, settingId) {
            new Ajax.Request('{$ajaxUrl}', {
                method: 'post',
                parameters: {
                    action: 'get',
                    setting_id: settingId,
                    form_key: FORM_KEY
                },
                onSuccess: function(response) {
                    try {
                        var result = response.responseText.evalJSON();
                        if (result.success && result.data && result.data.value !== undefined) {
                            var element = $(elementId);
                            if (element) {
                                if (settingId === 'minify' && result.data.value) {
                                    // Handle minify multiselect
                                    var minifyValues = [];
                                    if (result.data.value.css === 'on') minifyValues.push('css');
                                    if (result.data.value.html === 'on') minifyValues.push('html');
                                    if (result.data.value.js === 'on') minifyValues.push('js');
                                    
                                    // Update multiselect options
                                    for (var i = 0; i < element.options.length; i++) {
                                        element.options[i].selected = minifyValues.indexOf(element.options[i].value) !== -1;
                                    }
                                } else {
                                    element.value = result.data.value;
                                }
                            }
                        }
                    } catch (e) {
                        console.error('Error parsing zone setting response:', e);
                    }
                },
                onFailure: function() {
                    console.error('Failed to load zone setting value for:', settingId);
                }
            });
        }
        
        function updateZoneSetting(elementId, settingId, value) {
            var element = $(elementId);
            if (!element) return;
            
            // Show loading state
            var originalBg = element.style.backgroundColor;
            element.style.backgroundColor = '#ffffcc';
            
            // Prepare value for minify setting
            var apiValue = value;
            if (settingId === 'minify') {
                var selectedValues = [];
                for (var i = 0; i < element.options.length; i++) {
                    if (element.options[i].selected) {
                        selectedValues.push(element.options[i].value);
                    }
                }
                
                apiValue = {
                    css: selectedValues.indexOf('css') !== -1 ? 'on' : 'off',
                    html: selectedValues.indexOf('html') !== -1 ? 'on' : 'off',
                    js: selectedValues.indexOf('js') !== -1 ? 'on' : 'off'
                };
            }
            
            new Ajax.Request('{$ajaxUrl}', {
                method: 'post',
                parameters: {
                    action: 'update',
                    setting_id: settingId,
                    value: Object.isObject(apiValue) ? Object.toJSON(apiValue) : apiValue,
                    form_key: FORM_KEY
                },
                onSuccess: function(response) {
                    try {
                        var result = response.responseText.evalJSON();
                        if (result.success) {
                            // Success - green background
                            element.style.backgroundColor = '#ccffcc';
                            setTimeout(function() {
                                element.style.backgroundColor = originalBg;
                            }, 2000);
                        } else {
                            // Error - red background
                            element.style.backgroundColor = '#ffcccc';
                            alert('Error updating setting: ' + result.message);
                            setTimeout(function() {
                                element.style.backgroundColor = originalBg;
                            }, 3000);
                        }
                    } catch (e) {
                        element.style.backgroundColor = '#ffcccc';
                        alert('Error: Invalid response format');
                        setTimeout(function() {
                            element.style.backgroundColor = originalBg;
                        }, 3000);
                    }
                },
                onFailure: function() {
                    element.style.backgroundColor = '#ffcccc';
                    alert('Error: Failed to update zone setting');
                    setTimeout(function() {
                        element.style.backgroundColor = originalBg;
                    }, 3000);
                }
            });
        }
        //]]>
        </script>";
    }
}
